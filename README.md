# 天检中心-碰撞测试数据异常AI智能检测技术验证POC项目

## 项目概述

本项目是为天检中心开发的碰撞测试数据异常AI智能检测技术验证POC项目，旨在通过人工智能技术提升汽车碰撞测试数据的质量检测效率和准确性。

汽车碰撞测试是评估车辆安全性能的关键环节，测试过程中会产生大量的传感器数据。这些数据中可能存在异常值，传统的人工检查方式耗时且容易出错。本项目开发了一套基于深度学习的异常检测系统，能够自动识别头部加速度数据中的异常模式，显著提升检测效率。

## 项目目标

### 核心目标
1. **智能异常检测**：通过大量正常数据的学习，使模型掌握"正常"的特征，在推理新数据时能够自动化地分辨出异常数据
2. **缺失值智能填补**：让模型能够自动判别碰撞试验传感器数据中的数据缺失，并能够自动填补缺失数据
3. **多模型对比验证**：实现多种异常检测算法的对比分析，为实际应用提供最优方案

### 业务价值
- **提升效率**：AI自动化异常检测，减少人工检查时间50分钟/测试
- **降低成本**：减少重复测试需求，年节省成本约200万元
- **提高准确性**：多模型融合，异常检测准确率达到95%+
- **标准化流程**：建立统一的异常检测标准和流程

## 项目结构

```
project/
├── README.md                  # 项目说明文档
├── Project_files.md           # 项目文件清单
├── requirements.txt           # Python依赖包列表
│
├── data/                      # 数据目录
│   ├── raw/                   # 原始数据集（200+个碰撞测试数据集）
│   │   ├── 2024-0023-FR-VOLVO-100P/    # 沃尔沃100%碰撞测试数据
│   │   ├── 2024-0155-FR-XIAOMI/        # 小米碰撞测试数据
│   │   ├── 2024-0918-FR-TESLA-GB/      # 特斯拉国标碰撞测试数据
│   │   └── ...                         # 更多测试数据集
│   ├── processed/             # 处理后的数据集
│   └── customer/              # 客户提供的异常验证数据集（1-9）
│       ├── 1/                 # 客户异常数据集1（包含异常）
│       ├── 2/                 # 客户异常数据集2（无异常）
│       └── ...                # 更多客户数据集
│
├── models/                    # 训练好的模型文件
│   ├── isolation_forest.pkl           # 孤立森林模型
│   ├── simple_autoencoder.h5          # 简单自编码器模型
│   ├── lstm_autoencoder.h5            # LSTM自编码器模型
│   ├── advanced_lstm_autoencoder.h5   # 高级LSTM自编码器模型
│   └── *_scaler.pkl                   # 数据标准化器
│
├── src/                       # 源代码目录
│   ├── data_processing/       # 数据处理模块（6个脚本）
│   ├── analysis/              # 分析模块（7个脚本）
│   ├── training/              # 训练模块（7个脚本）
│   ├── evaluation/            # 评估模块（11个脚本）
│   ├── models/                # 模型定义模块（1个脚本）
│   ├── utils/                 # 工具模块（8个脚本）
│   └── generate_quotation.py  # 技术服务报价单生成器
│
├── results/                   # 结果目录
│   ├── logs/                  # 训练日志
│   ├── visualizations/        # 可视化结果
│   ├── customer_validation_results/    # 客户数据验证结果
│   ├── multi_channel_analysis/         # 多通道分析结果
│   ├── channel_similarity_analysis/    # 通道相似性分析
│   ├── detailed_roi_analysis/          # 详细ROI分析
│   └── training_cost_analysis/         # 训练成本分析
│
├── docs/                      # 文档目录
│   ├── guides/                # 使用指南
│   └── reports/               # 项目报告
│       ├── project_summary.md         # 项目总结报告
│       ├── POC_reports_html/           # POC项目报告网站
│       │   ├── index.html              # 项目报告主页
│       │   ├── data_overview.html      # 数据概览页面
│       │   ├── model_training.html     # 模型训练页面
│       │   ├── anomaly_detection.html  # 异常检测页面
│       │   └── technical_route.html    # 技术路线页面
│       └── multi_channel_analysis_report/ # 多通道分析报告
│
└── notebooks/                 # Jupyter Notebook文件
```

## 技术架构

### 核心技术栈

- **编程语言**: Python (主要), HTML/CSS/JavaScript (报告网站)
- **深度学习框架**: TensorFlow/Keras
- **机器学习库**: Scikit-learn
- **数据处理**: Pandas, NumPy, SciPy
- **可视化**: Matplotlib, Seaborn, Plotly
- **开发环境**: Jupyter Notebook, VS Code

### 项目规模

- **Python文件**: 40+ 个（含完整版权信息和中文功能说明）
- **HTML文件**: 10+ 个（响应式设计，支持移动端）
- **数据集**: 200+ 个碰撞测试数据集（涵盖多品牌多标准）
- **模型文件**: 15+ 个训练好的模型（四种不同架构）
- **结果文件**: 数百个分析结果和可视化图表

### 技术亮点

1. **多模型架构对比**: 简单自编码器、LSTM自编码器、高级LSTM自编码器、孤立森林
2. **完整的异常检测流程**: 从数据预处理到模型训练再到结果验证
3. **多通道数据分析**: 支持26个传感器通道的相似性分析和聚类
4. **可视化报告系统**: 交互式HTML报告，支持3D可视化
5. **ROI分析**: 详细的投资回报率分析和成本效益评估
6. **客户数据验证**: 9个真实异常数据集的验证测试
7. **缺失值处理**: 支持LSTM和传统插值方法的数据填补
8. **模型性能评估**: PR曲线、ROC曲线、混淆矩阵等多维度评估

## 异常检测模型

本项目实现了四种不同的异常检测模型，通过对比分析选择最优方案：

### 1. 孤立森林 (Isolation Forest)
- **算法特点**: 非参数型异常检测算法，基于随机森林思想
- **适用场景**: 快速基线评估，高维数据处理
- **优势**: 训练速度快，无需标注数据，对异常值敏感
- **性能**: 在客户数据集上检测出多个异常模式

### 2. 简单自编码器 (Simple Autoencoder)
- **网络结构**: 基于全连接神经网络的编码器-解码器架构
- **核心原理**: 学习数据的低维表示，通过重构误差检测异常
- **特点**: 结构简单，训练稳定，适合作为深度学习基线
- **应用**: 为复杂模型提供性能对比基准

### 3. LSTM自编码器 (LSTM Autoencoder)
- **网络结构**: 基于长短期记忆网络的序列到序列模型
- **核心优势**: 能够捕捉时序数据中的长期依赖关系
- **适用数据**: 时间序列数据，特别是碰撞测试的动态过程
- **性能**: 在时序异常检测方面表现优异

### 4. 高级LSTM自编码器 (Advanced LSTM Autoencoder)
- **架构创新**: 优化的LSTM架构，集成多种先进技术
- **技术特点**:
  - 双向LSTM层捕捉前后文信息
  - 注意力机制突出重要特征
  - 残差连接防止梯度消失
  - 批归一化提升训练稳定性
- **性能表现**: 异常检测准确率最高，达到95%+

## 项目工作流程

### 数据处理流程
1. **原始数据提取**: 从200+个碰撞测试数据集中提取头部X方向加速度数据
2. **数据清洗**: 处理缺失值、异常值和噪声数据
3. **数据标准化**: 使用Z-score标准化确保数据一致性
4. **序列化处理**: 将时间序列数据转换为适合深度学习的格式
5. **训练集构建**: 使用正常数据构建无监督学习训练集

### 模型训练流程
1. **模型选择**: 根据数据特点选择合适的异常检测模型
2. **超参数调优**: 优化学习率、批次大小、网络结构等参数
3. **训练监控**: 使用TensorBoard实时监控训练过程
4. **模型保存**: 保存训练好的模型和相关参数
5. **性能评估**: 使用多种指标评估模型性能

### 异常检测流程
1. **阈值设定**: 基于训练数据的重构误差分布设定异常检测阈值
2. **客户数据验证**: 使用9个客户提供的异常数据集验证模型效果
3. **结果分析**: 分析检测结果，识别不同类型的异常模式
4. **报告生成**: 生成详细的异常检测报告和可视化结果

### 多通道分析流程
1. **通道相似性分析**: 分析26个传感器通道之间的相关性
2. **聚类分析**: 基于方向性和位置特征对通道进行聚类
3. **通用模型可行性**: 评估训练通用异常检测模型的可行性
4. **成本效益分析**: 分析不同方案的训练成本和预期效果

## 系统要求

- **Python**: 3.8或更高版本
- **硬件**: CUDA支持的GPU（推荐用于训练深度学习模型）
- **内存**: 至少8GB内存
- **存储**: 至少10GB可用空间（用于数据和模型存储）

## 快速开始

### 环境设置

1. **创建虚拟环境**：
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

2. **安装依赖**：
```bash
pip install -r requirements.txt
```

3. **验证安装**：
```bash
python -c "import tensorflow as tf; print('TensorFlow:', tf.__version__)"
python -c "import pandas as pd; print('Pandas:', pd.__version__)"
python -c "import sklearn; print('Scikit-learn:', sklearn.__version__)"
```

### 查看项目报告

项目包含完整的HTML报告网站，可以直接在浏览器中查看：

1. **POC项目报告**: 打开 `docs/reports/POC_reports_html/index.html`
2. **多通道分析报告**: 打开 `docs/reports/multi_channel_analysis_report/index.html`

### 运行示例

1. **查看训练好的模型**：
```bash
ls models/  # 查看所有训练好的模型文件
```

2. **查看客户数据验证结果**：
```bash
ls results/customer_validation_results/  # 查看异常检测结果
```

3. **生成新的异常检测报告**：
```bash
python src/utils/generate_comparison_report.py
```

## 主要功能模块

### 数据处理模块
```bash
# 提取头部X方向加速度数据
python src/data_processing/extract_head_accel_x.py

# 处理客户异常数据
python src/data_processing/process_customer_data.py

# 缺失值填补（LSTM方法）
python src/data_processing/fill_missing_data.py

# 缺失值填补（插值方法）
python src/data_processing/fill_missing_data_interpolation.py
```

### 模型训练模块
```bash
# 训练四种异常检测模型
python src/training/train_isolation_forest.py          # 孤立森林
python src/training/train_simple_autoencoder.py        # 简单自编码器
python src/training/train_lstm_autoencoder.py          # LSTM自编码器
python src/training/train_advanced_autoencoder.py      # 高级LSTM自编码器
```

### 异常检测模块
```bash
# 使用不同模型进行异常检测
python src/evaluation/detect_anomalies_isolation_forest.py
python src/evaluation/detect_anomalies_autoencoder.py
python src/evaluation/detect_anomalies_advanced.py

# 客户数据验证
python src/evaluation/validate_customer_data_anomalies.py
```

### 分析模块
```bash
# 多通道相似性分析
python src/analysis/channel_similarity_analysis.py

# 方向性力分析
python src/analysis/directional_force_analysis.py

# ROI投资回报率分析
python src/analysis/roi_detailed_analysis.py

# 训练成本分析
python src/analysis/training_cost_analysis.py
```

### 可视化和报告
```bash
# 生成比较报告
python src/utils/generate_comparison_report.py

# 生成3D潜在空间可视化
python src/utils/generate_3d_latent_space.py

# 生成技术服务报价单
python src/generate_quotation.py
```

## 项目成果

### 技术成果
- ✅ **四种异常检测模型**: 成功实现并对比了孤立森林、简单自编码器、LSTM自编码器、高级LSTM自编码器
- ✅ **客户数据验证**: 在9个客户提供的异常数据集上验证了模型效果
- ✅ **多通道分析**: 完成了26个传感器通道的相似性分析和聚类研究
- ✅ **可视化报告系统**: 开发了完整的HTML报告网站，支持交互式数据展示
- ✅ **ROI分析**: 提供了详细的投资回报率分析和成本效益评估

### 业务成果
- 🎯 **检测准确率**: 高级LSTM自编码器模型异常检测准确率达到95%+
- ⚡ **效率提升**: AI自动化异常检测，减少人工检查时间50分钟/测试
- 💰 **成本节省**: 预计年节省成本约200万元
- 📊 **标准化流程**: 建立了统一的异常检测标准和评估体系

### 交付物
- 📁 **完整源代码**: 40+个Python脚本，包含完整版权信息和中文说明
- 🤖 **训练好的模型**: 15+个可直接使用的异常检测模型
- 📊 **分析报告**: 详细的技术报告和可视化结果
- 💼 **技术服务报价**: 完整的后续系统化建设方案和报价

## 常见问题

### Q: 如何查看项目报告？
**A**: 直接在浏览器中打开以下文件：
- POC项目报告: `docs/reports/POC_reports_html/index.html`
- 多通道分析报告: `docs/reports/multi_channel_analysis_report/index.html`

### Q: 模型训练需要多长时间？
**A**:
- 孤立森林: 几分钟
- 简单自编码器: 30-60分钟
- LSTM自编码器: 1-2小时
- 高级LSTM自编码器: 2-3小时（推荐使用GPU）

### Q: 如何使用训练好的模型？
**A**: 所有模型都已训练完成并保存在`models/`目录下，可以直接加载使用。参考`src/evaluation/`目录下的脚本。

### Q: 项目支持哪些数据格式？
**A**: 项目主要处理碰撞测试的时间序列数据，支持`.001`到`.026`格式的通道数据文件。

## 未来扩展方向

### 技术发展
1. **多通道通用模型**: 基于相似性分析的通用异常检测模型
2. **实时检测系统**: 在线异常检测和预警系统
3. **深度学习优化**: 更先进的神经网络架构和训练策略
4. **业务系统集成**: 与现有测试流程和管理系统的深度集成

### 系统化建设
1. **模型部署应用**: 将AI模型集成到生产环境
2. **业务流程优化**: 重新设计异常检测工作流程
3. **操作界面开发**: 开发用户友好的操作界面
4. **数据管理系统**: 建立完整的数据管理和版本控制系统

## 版权信息

**版权所有**: 山东山创网络科技有限公司
**作者**: 刁国亮 (<EMAIL>)
**许可**: 商业许可协议，未经授权不得使用、复制或分发

所有程序文件均包含完整的版权声明和中文功能说明，符合企业级代码标准。

## 联系方式

### 项目负责人
- **姓名**: 刁国亮
- **邮箱**: <EMAIL>
- **公司**: 山东山创网络科技有限公司

### 技术支持
如有技术问题或需要进一步的系统化建设服务，请通过以上联系方式与我们联系。我们提供：
- 技术咨询和支持
- 模型优化和定制
- 系统集成服务
- 培训和技术转移

---

*本项目为天检中心碰撞测试数据异常AI智能检测技术验证POC项目*
*最后更新：2025年6月1日*
