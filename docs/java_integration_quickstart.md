# Java系统集成快速开始指南
## 碰撞测试数据异常AI智能检测系统

### 概述
本指南提供了将现有Python AI模型集成到Java企业级系统的快速实施方案。基于Spring Boot + FastAPI的微服务架构，实现AI异常检测能力的系统化应用。

---

## 1. 核心架构

```
Java应用 (Spring Boot) ←→ AI服务 (FastAPI) ←→ 训练好的模型
     ↓                        ↓                    ↓
   MySQL数据库              Redis缓存           TensorFlow模型
```

### 技术栈选择
- **Java后端**: Spring Boot 2.7+, Spring Security, JPA
- **AI服务**: FastAPI, TensorFlow, Pandas
- **数据库**: MySQL 8.0, Redis 6.2
- **部署**: Docker, Docker Compose

---

## 2. AI服务快速搭建

### 2.1 创建FastAPI服务

```python
# ai_service/main.py
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import tensorflow as tf
import numpy as np
import pandas as pd
import joblib
from typing import List, Dict, Any

app = FastAPI(title="碰撞测试异常检测AI服务", version="1.0.0")

# 全局模型加载器
class ModelManager:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.thresholds = {}
        self.load_models()
    
    def load_models(self):
        """加载所有训练好的模型"""
        try:
            # 加载高级LSTM自编码器
            self.models['advanced_lstm'] = tf.keras.models.load_model(
                '/app/models/advanced_lstm_autoencoder.h5', compile=False)
            self.scalers['advanced_lstm'] = joblib.load(
                '/app/models/advanced_lstm_scaler.pkl')
            self.thresholds['advanced_lstm'] = np.load(
                '/app/models/advanced_lstm_threshold.npy')
            
            # 加载其他模型...
            print("所有模型加载完成")
        except Exception as e:
            print(f"模型加载失败: {e}")

model_manager = ModelManager()

# 请求响应模型
class DetectionRequest(BaseModel):
    project_id: str
    test_id: str
    data_type: str = "head_accel_x"
    model_type: str = "advanced_lstm"
    data: List[Dict[str, Any]]
    options: Dict[str, Any] = {}

class AnomalyInfo(BaseModel):
    start_index: int
    end_index: int
    severity: str
    confidence: float
    type: str

class DetectionResponse(BaseModel):
    detection_id: str
    summary: Dict[str, Any]
    anomalies: List[AnomalyInfo]
    visualization: Dict[str, str]

@app.post("/api/v1/detection/anomaly", response_model=DetectionResponse)
async def detect_anomalies(request: DetectionRequest):
    """异常检测主接口"""
    try:
        # 1. 数据预处理
        df = pd.DataFrame(request.data)
        processed_data = preprocess_data(df, request.model_type)
        
        # 2. 模型推理
        model = model_manager.models[request.model_type]
        scaler = model_manager.scalers[request.model_type]
        threshold = model_manager.thresholds[request.model_type]
        
        # 3. 异常检测
        anomalies = perform_detection(model, processed_data, scaler, threshold)
        
        # 4. 构造响应
        response = DetectionResponse(
            detection_id=f"det_{request.test_id}_{int(time.time())}",
            summary={
                "total_sequences": len(processed_data),
                "anomaly_count": len(anomalies),
                "anomaly_rate": len(anomalies) / len(processed_data) * 100
            },
            anomalies=anomalies,
            visualization={
                "chart_url": f"/api/v1/charts/{request.test_id}",
                "report_url": f"/api/v1/reports/{request.test_id}"
            }
        )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

def preprocess_data(df: pd.DataFrame, model_type: str):
    """数据预处理"""
    # 实现数据标准化和序列化逻辑
    pass

def perform_detection(model, data, scaler, threshold):
    """执行异常检测"""
    # 实现异常检测逻辑
    pass

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "models_loaded": len(model_manager.models)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2.2 AI服务Dockerfile

```dockerfile
# ai_service/Dockerfile
FROM python:3.8-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码和模型
COPY . .
COPY ../models ./models

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

---

## 3. Java应用快速搭建

### 3.1 Spring Boot项目结构

```
src/main/java/com/shanchuang/crashtest/
├── CrashTestApplication.java
├── config/
│   └── AiServiceConfig.java
├── controller/
│   └── DetectionController.java
├── service/
│   ├── DetectionService.java
│   └── AiModelClient.java
├── entity/
│   └── DetectionResult.java
└── dto/
    ├── DetectionRequest.java
    └── DetectionResponse.java
```

### 3.2 核心代码实现

#### 配置类
```java
// config/AiServiceConfig.java
@Configuration
public class AiServiceConfig {
    
    @Value("${ai.service.base-url:http://localhost:8000}")
    private String aiServiceBaseUrl;
    
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置超时时间
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(30000);
        factory.setReadTimeout(300000);
        restTemplate.setRequestFactory(factory);
        
        return restTemplate;
    }
    
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(new JavaTimeModule());
    }
}
```

#### AI服务客户端
```java
// service/AiModelClient.java
@Service
@Slf4j
public class AiModelClient {
    
    @Value("${ai.service.base-url}")
    private String aiServiceBaseUrl;
    
    private final RestTemplate restTemplate;
    
    public AiModelClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    public DetectionResponse detectAnomalies(DetectionRequest request) {
        try {
            String url = aiServiceBaseUrl + "/api/v1/detection/anomaly";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<DetectionRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<DetectionResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, DetectionResponse.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("AI服务调用失败", e);
            throw new RuntimeException("异常检测服务不可用", e);
        }
    }
    
    public boolean isServiceHealthy() {
        try {
            String url = aiServiceBaseUrl + "/health";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 检测服务
```java
// service/DetectionService.java
@Service
@Transactional
public class DetectionService {
    
    private final AiModelClient aiModelClient;
    
    public DetectionService(AiModelClient aiModelClient) {
        this.aiModelClient = aiModelClient;
    }
    
    public DetectionResult performDetection(DetectionRequest request) {
        // 1. 验证请求参数
        validateRequest(request);
        
        // 2. 调用AI服务
        DetectionResponse aiResponse = aiModelClient.detectAnomalies(request);
        
        // 3. 保存结果到数据库
        DetectionResult result = new DetectionResult();
        result.setProjectId(request.getProjectId());
        result.setTestId(request.getTestId());
        result.setModelType(request.getModelType());
        result.setAnomalyCount(aiResponse.getSummary().get("anomaly_count"));
        result.setDetectionTime(LocalDateTime.now());
        result.setStatus("COMPLETED");
        
        // 保存到数据库的逻辑...
        
        return result;
    }
    
    private void validateRequest(DetectionRequest request) {
        if (request.getData() == null || request.getData().isEmpty()) {
            throw new IllegalArgumentException("检测数据不能为空");
        }
        // 其他验证逻辑...
    }
}
```

#### 控制器
```java
// controller/DetectionController.java
@RestController
@RequestMapping("/api/v1/detection")
@Slf4j
public class DetectionController {
    
    private final DetectionService detectionService;
    
    public DetectionController(DetectionService detectionService) {
        this.detectionService = detectionService;
    }
    
    @PostMapping("/anomaly")
    public ResponseEntity<ApiResponse<DetectionResult>> detectAnomalies(
            @RequestBody DetectionRequest request) {
        try {
            DetectionResult result = detectionService.performDetection(request);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("异常检测失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("检测失败: " + e.getMessage()));
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("ai_service", detectionService.isAiServiceHealthy());
        return ResponseEntity.ok(health);
    }
}
```

---

## 4. 快速部署

### 4.1 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Java应用
  crash-test-api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - AI_SERVICE_URL=http://ai-service:8000
      - DB_HOST=mysql
    depends_on:
      - mysql
      - ai-service
    networks:
      - crash-test-net

  # AI服务
  ai-service:
    build: ./ai-service
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models:ro
    networks:
      - crash-test-net

  # 数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=crash_test_db
    ports:
      - "3306:3306"
    networks:
      - crash-test-net

networks:
  crash-test-net:
    driver: bridge
```

### 4.2 一键启动

```bash
# 1. 克隆项目并进入目录
git clone <your-repo>
cd crash-test-system

# 2. 复制模型文件到指定目录
cp -r /path/to/your/models ./models/

# 3. 构建并启动所有服务
docker-compose up --build

# 4. 验证服务状态
curl http://localhost:8080/api/v1/detection/health
curl http://localhost:8000/health
```

---

## 5. 使用示例

### 5.1 API调用示例

```bash
# 异常检测请求
curl -X POST http://localhost:8080/api/v1/detection/anomaly \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "proj-001",
    "testId": "test-001",
    "modelType": "advanced_lstm",
    "data": [
      {"timestamp": "2024-01-01T00:00:00Z", "value": 1.23},
      {"timestamp": "2024-01-01T00:00:01Z", "value": 1.25}
    ]
  }'
```

### 5.2 Java客户端调用

```java
// 在你的业务代码中
@Autowired
private DetectionService detectionService;

public void performAnomalyDetection() {
    DetectionRequest request = new DetectionRequest();
    request.setProjectId("proj-001");
    request.setTestId("test-001");
    request.setModelType("advanced_lstm");
    
    // 设置检测数据
    List<Map<String, Object>> data = Arrays.asList(
        Map.of("timestamp", "2024-01-01T00:00:00Z", "value", 1.23),
        Map.of("timestamp", "2024-01-01T00:00:01Z", "value", 1.25)
    );
    request.setData(data);
    
    // 执行检测
    DetectionResult result = detectionService.performDetection(request);
    
    System.out.println("检测完成，异常数量: " + result.getAnomalyCount());
}
```

---

## 6. 监控与维护

### 6.1 健康检查
- Java应用: `http://localhost:8080/actuator/health`
- AI服务: `http://localhost:8000/health`

### 6.2 日志查看
```bash
# 查看Java应用日志
docker-compose logs -f crash-test-api

# 查看AI服务日志
docker-compose logs -f ai-service
```

### 6.3 性能监控
- 使用Spring Boot Actuator监控Java应用性能
- 使用FastAPI内置监控查看AI服务状态
- 通过Prometheus + Grafana实现完整监控

---

## 7. 常见问题

### Q: 模型加载失败怎么办？
**A**: 检查模型文件路径和权限，确保Docker容器能访问模型文件。

### Q: AI服务响应慢怎么优化？
**A**: 
1. 增加AI服务实例数量
2. 使用模型量化减少推理时间
3. 实现结果缓存机制

### Q: 如何扩展支持更多模型？
**A**: 在ModelManager中添加新模型的加载逻辑，并在API中支持新的model_type参数。

---

**快速开始指南完成！** 🎉

按照本指南，您可以在30分钟内搭建起完整的Java+AI异常检测系统。如需更详细的配置和优化，请参考完整的技术设计文档。
