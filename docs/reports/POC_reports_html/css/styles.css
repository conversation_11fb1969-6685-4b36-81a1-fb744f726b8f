/*
Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
All rights reserved.

Author: diaoguoliang
Email: <EMAIL>
Date: 2025-05-30

This software is proprietary and confidential. It is licensed under a
commercial license agreement and may not be used, copied, or distributed
without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

POC项目报告网站自定义样式表
为天检中心碰撞测试数据异常AI智能检测技术验证POC项目报告网站提供样式定义，
包含主题色彩、动画效果、响应式布局、图表样式等。
支持打印样式和移动端适配。
*/

/* 自定义样式 */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1e40af;
    --primary-light: #dbeafe;
    --secondary-color: #10b981;
    --text-color: #1f2937;
    --light-text: #6b7280;
    --background-color: #f9fafb;
    --card-background: #ffffff;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 卡片悬停效果 */
.card-hover {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 代码块样式 */
pre {
    background-color: #f1f5f9;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 1rem 0;
}

code {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f1f5f9;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

table th {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    font-weight: 600;
    text-align: left;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid var(--primary-color);
}

table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

table tr:nth-child(even) {
    background-color: #f9fafb;
}

table tr:hover {
    background-color: #f1f5f9;
}

/* 引用样式 */
blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1rem 0;
    color: var(--light-text);
    font-style: italic;
}

/* 图表容器 */
.chart-container {
    width: 100%;
    height: 400px;
    margin: 2rem 0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.tag-secondary {
    background-color: #d1fae5;
    color: #065f46;
}

.tag-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.tag-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

/* 进度条样式 */
.progress-container {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 9999px;
    transition: width 0.5s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 打印样式 */
@media print {
    nav, footer, .no-print {
        display: none !important;
    }

    body {
        background-color: white;
    }

    main {
        padding: 0;
        margin: 0;
    }

    .container {
        max-width: 100%;
        padding: 0;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    img, table {
        page-break-inside: avoid;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }

    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }
}
