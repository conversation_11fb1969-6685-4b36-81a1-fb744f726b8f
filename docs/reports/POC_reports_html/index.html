<!--
Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
All rights reserved.

Author: diaoguoliang
Email: <EMAIL>
Date: 2025-05-30

This software is proprietary and confidential. It is licensed under a
commercial license agreement and may not be used, copied, or distributed
without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

天检中心POC项目报告主页
展示碰撞测试数据异常AI智能检测技术验证POC项目的完整报告，
包含项目概述、主要成果、进展概览、发现总结等内容。
提供响应式设计和交互式功能，支持移动端访问。
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天检中心-碰撞测试数据异常AI智能检测技术验证POC - 项目报告</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Lottie -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 导航栏 -->
    <nav class="bg-blue-800 text-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="text-xl font-bold">天检中心-碰撞测试数据异常AI智能检测技术验证POC</div>
                <div class="hidden md:flex space-x-6">
                    <a href="index.html" class="hover:text-blue-200 transition font-bold">首页</a>
                    <a href="data_overview.html" class="hover:text-blue-200 transition">数据集理解分析</a>
                    <a href="model_training.html" class="hover:text-blue-200 transition">模型构建训练</a>
                    <a href="anomaly_detection.html" class="hover:text-blue-200 transition">异常检测验证</a>
                    <a href="technical_route.html" class="hover:text-blue-200 transition">技术路线与展望</a>
                    <!-- <a href="value_analysis.html" class="hover:text-blue-200 transition">价值分析</a> -->
                </div>
                <button id="mobile-menu-button" class="md:hidden focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div id="mobile-menu" class="md:hidden hidden pt-4 pb-2">
                <a href="index.html" class="block py-2 hover:text-blue-200 transition font-bold">首页</a>
                <a href="data_overview.html" class="block py-2 hover:text-blue-200 transition">数据集理解分析</a>
                <a href="model_training.html" class="block py-2 hover:text-blue-200 transition">模型构建训练</a>
                <a href="anomaly_detection.html" class="block py-2 hover:text-blue-200 transition">异常检测验证</a>
                <a href="technical_route.html" class="block py-2 hover:text-blue-200 transition">技术路线与展望</a>
                <!-- <a href="value_analysis.html" class="block py-2 hover:text-blue-200 transition">价值分析</a> -->
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 欢迎动画 -->
        <div class="flex justify-center mb-8">
            <lottie-player src="https://assets5.lottiefiles.com/packages/lf20_qp1q7mct.json" background="transparent" speed="1" style="width: 300px; height: 300px;" loop autoplay></lottie-player>
        </div>

        <!-- 项目标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-blue-800 mb-4">天检中心-碰撞测试数据异常AI智能检测技术验证POC</h1>
            <p class="text-xl text-gray-600">基于深度学习技术的数据异常检测解决方案</p>
        </div>

        <!-- 项目概述 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">项目概述</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <p class="mb-4">本项目旨在开发一个基于深度学习的异常检测系统，用于分析汽车碰撞测试数据中的异常模式。系统能够自动识别头部加速度数据中的异常值，帮助工程师快速定位潜在的测试问题或设备故障。</p>

                <div class="grid md:grid-cols-2 gap-6 mt-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">项目目标</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>开发数据预处理流程，处理原始碰撞测试数据</li>
                            <li>实现多种异常检测模型，包括传统机器学习方法和深度学习方法</li>
                            <li>评估不同模型的性能，选择最适合的模型架构</li>
                            <li>应用选定的模型处理天检碰撞中心数采组提供的异常数据集</li>
                            <li>生成详细的异常检测报告和可视化结果</li>
                        </ul>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">主要技术</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>数据处理: Pandas, NumPy, SciPy</li>
                            <li>机器学习: Scikit-learn, TensorFlow, Keras</li>
                            <li>可视化: Matplotlib, Seaborn</li>
                            <li>监控工具: TensorBoard</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主要成果 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">主要成果</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <!-- 核心要素指标卡片 -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-blue-700 mb-4 text-center">项目核心成果指标</h3>
                    <div class="grid md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-blue-700 mb-2">4</div>
                            <div class="text-sm font-medium text-gray-700">异常检测模型</div>
                            <div class="mt-2 text-xs text-gray-600">孤立森林、简单自编码器、LSTM自编码器、高级LSTM自编码器</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-blue-700 mb-2">87%</div>
                            <div class="text-sm font-medium text-gray-700">断点异常检出率</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-blue-700 mb-2">85%</div>
                            <div class="text-sm font-medium text-gray-700">尖峰异常检出率</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-blue-700 mb-2">81%</div>
                            <div class="text-sm font-medium text-gray-700">波动异常检出率</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-3 gap-4 mt-4">
                        <div class="bg-green-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-green-700 mb-2">0.89</div>
                            <div class="text-sm font-medium text-gray-700">精确率</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-green-700 mb-2">0.87</div>
                            <div class="text-sm font-medium text-gray-700">召回率</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 shadow-sm text-center">
                            <div class="text-3xl font-bold text-green-700 mb-2">0.88</div>
                            <div class="text-sm font-medium text-gray-700">F1分数</div>
                            <div class="mt-2 text-xs text-gray-600">高级LSTM自编码器模型</div>
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <a href="model_training.html" class="text-blue-600 hover:text-blue-800 text-sm inline-block">
                            查看详细模型性能指标 →
                        </a>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">数据处理流程</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>建立了完整的数据预处理流程，从原始数据提取到标准化处理</li>
                            <li>实现了多种缺失值填充方法，提高了数据质量</li>
                            <li>开发了序列化处理技术，适应深度学习模型的输入要求</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">异常检测模型</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>实现了多种异常检测模型，包括孤立森林、简单自编码器、LSTM自编码器和高级LSTM自编码器</li>
                            <li>优化了模型架构和超参数，提高了检测准确率</li>
                            <li>添加了模型保存和加载功能，便于后续应用</li>
                        </ul>
                    </div>
                </div>
                <div class="grid md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">评估框架</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>开发了全面的评估框架，包括多种评估指标和可视化功能</li>
                            <li>实现了模型比较功能，便于选择最适合的模型</li>
                            <li>添加了TensorBoard支持，实现训练过程的实时监控</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">客户数据分析</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>成功处理了天检碰撞中心数采组提供的异常数据集，包括新的/8和/9目录</li>
                            <li>应用优化后的模型进行异常检测，生成了详细的结果</li>
                            <li>比较了新旧数据集的异常检测结果，提供了深入的分析</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 项目进展概览 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">项目进展概览</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="border rounded-lg p-4 hover:shadow-md transition">
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">第一天 (2025-05-14)</h3>
                        <ul class="list-disc pl-5 space-y-1 text-sm">
                            <li>数据探索与预处理</li>
                            <li>数据提取与清洗</li>
                            <li>基础模型开发</li>
                        </ul>
                        <button onclick="toggleWorkLog('day1Content')" class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">查看详情 ↓</button>
                        <div id="day1Content" class="hidden mt-3 pt-3 border-t text-sm">
                            <h4 class="font-semibold text-blue-600 mb-1">数据分析</h4>
                            <p class="mb-2">分析了原始碰撞测试数据的结构和特征，确定了头部X方向加速度数据作为主要分析对象，识别了数据中的缺失值和异常值模式。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">数据提取</h4>
                            <p class="mb-2">开发了提取脚本，从原始数据中提取头部X方向加速度数据，实现了数据格式转换，将原始格式转换为CSV格式，添加了测试ID和时间戳信息。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">数据清洗</h4>
                            <p class="mb-2">开发了缺失值处理脚本，实现了多种插值方法，包括线性插值、样条插值和前向/后向填充，比较了不同插值方法的效果。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">基础模型开发</h4>
                            <p>实现了基于孤立森林的异常检测基线模型，开发了简单自编码器模型，添加了重构误差计算和异常检测逻辑。</p>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 hover:shadow-md transition">
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">第二天 (2025-05-15)</h3>
                        <ul class="list-disc pl-5 space-y-1 text-sm">
                            <li>高级模型开发</li>
                            <li>模型评估框架</li>
                            <li>客户数据处理</li>
                        </ul>
                        <button onclick="toggleWorkLog('day2Content')" class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">查看详情 ↓</button>
                        <div id="day2Content" class="hidden mt-3 pt-3 border-t text-sm">
                            <h4 class="font-semibold text-blue-600 mb-1">LSTM自编码器模型</h4>
                            <p class="mb-2">开发了基于LSTM的自编码器模型，提高了时序数据的异常检测能力，优化了模型架构，包括编码器和解码器的设计。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">模型评估框架</h4>
                            <p class="mb-2">开发了全面的评估功能，添加了多种评估指标，包括重构误差分布、PR曲线、ROC曲线等，实现了混淆矩阵和异常检测可视化功能。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">训练监控</h4>
                            <p class="mb-2">添加了TensorBoard支持，实现训练过程的实时监控，实现了训练历史记录和可视化功能。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">客户数据处理</h4>
                            <p>处理了天检碰撞中心数采组提供的异常数据集，实现了数据格式转换和标准化处理，应用已训练模型进行异常检测，生成了异常检测结果和可视化。</p>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 hover:shadow-md transition">
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">第三天 (2025-05-16)</h3>
                        <ul class="list-disc pl-5 space-y-1 text-sm">
                            <li>高级LSTM模型优化</li>
                            <li>新客户数据验证</li>
                            <li>项目整理与文档完善</li>
                        </ul>
                        <button onclick="toggleWorkLog('day3Content')" class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">查看详情 ↓</button>
                        <div id="day3Content" class="hidden mt-3 pt-3 border-t text-sm">
                            <h4 class="font-semibold text-blue-600 mb-1">模型改进</h4>
                            <p class="mb-2">开发了改进版LSTM自编码器模型，增强了对复杂模式的学习能力，解决了训练过程中的梯度问题和过拟合问题。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">可视化增强</h4>
                            <p class="mb-2">添加了潜空间可视化功能，帮助理解模型的特征提取能力，优化了训练历史记录和可视化功能。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">新客户数据验证</h4>
                            <p class="mb-2">处理了天检碰撞中心数采组提供的新异常数据集（/8和/9目录），应用优化后的模型进行异常检测，比较了新旧数据集的异常检测结果。</p>

                            <h4 class="font-semibold text-blue-600 mb-1">项目整理</h4>
                            <p>设计了新的项目结构，提高了代码的可维护性和可扩展性，更新了文档，提供了项目概述和使用指南，整理了代码结构，提高了可读性。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主要发现 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">主要发现</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div id="findings-chart" class="w-full h-80 mb-6"></div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">异常模式分析</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>原始数据集中的异常百分比范围为0%-11.60%，平均为5.05%</li>
                            <li>新数据集中的异常百分比范围为4.54%-5.45%，平均为4.84%</li>
                            <li>新数据集的异常分布更加集中，标准差仅为0.52%，而原始数据集为3.69%</li>
                            <li>8.001样本的异常百分比(5.45%)略高于9.001和9.013样本</li>
                            <li>9.001和9.013样本具有完全相同的异常百分比(4.54%)，可能具有相似的数据特征</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">模型性能比较</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>高级LSTM自编码器在大多数测试场景中表现最佳，特别是对于复杂的时序模式</li>
                            <li>LSTM模型比简单自编码器和孤立森林更适合捕捉时序异常</li>
                            <li>模型的重构误差分布可以有效区分正常和异常样本</li>
                            <li>潜在空间可视化显示模型能够有效区分不同类型的数据模式</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 经验教训 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">经验教训</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">数据质量至关重要</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>高质量的数据预处理对模型性能有显著影响</li>
                            <li>缺失值处理和标准化是关键步骤</li>
                            <li>数据可视化有助于理解数据特征和异常模式</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">模型选择需要权衡</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>简单模型（如孤立森林）计算效率高，但可能无法捕捉复杂模式</li>
                            <li>复杂模型（如LSTM自编码器）性能更好，但训练时间更长</li>
                            <li>模型复杂度应根据数据特征和应用需求来选择</li>
                        </ul>
                    </div>
                </div>
                <div class="grid md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">训练过程监控很重要</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>TensorBoard是监控训练过程的有力工具</li>
                            <li>保存训练历史数据有助于分析模型性能</li>
                            <li>错误处理和恢复机制可以提高训练稳定性</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">项目结构影响可维护性</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>良好的项目结构有助于代码的可维护性和可扩展性</li>
                            <li>文件分类和命名规范很重要</li>
                            <li>自动化工具可以简化项目管理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 未来工作 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">未来工作</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">模型改进</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>探索更复杂的神经网络架构，如Transformer或GRU</li>
                            <li>实现注意力机制，提高对关键时间点的敏感度</li>
                            <li>尝试半监督学习方法，利用少量标记数据提高性能</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">系统扩展</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>开发实时异常检测系统，支持在线数据流处理</li>
                            <li>添加多通道数据融合功能，综合分析多个传感器数据</li>
                            <li>实现异常原因分析功能，提供更深入的诊断信息</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-blue-700 mb-2">用户界面</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>开发Web界面，方便用户上传数据和查看结果</li>
                            <li>添加交互式可视化功能，支持深入探索异常区域</li>
                            <li>实现报告自动生成功能，提高工作效率</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 结论 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">结论</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <p class="mb-4">本项目成功开发了一个基于深度学习的异常检测系统，用于分析汽车碰撞测试数据中的异常模式。通过三天的工作，我实现了数据预处理、模型开发、评估框架和客户数据分析等关键功能。高级LSTM自编码器模型在异常检测任务中表现最佳，能够有效识别头部X方向加速度数据中的异常值。</p>

                <p class="mb-4">项目的成果不仅包括功能完善的代码，还包括详细的文档和可视化结果，为用户提供了全面的支持。通过项目结构的重组，我提高了代码的可维护性和可扩展性，为未来的开发奠定了基础。</p>

                <p>总的来说，本项目为汽车碰撞测试数据的异常检测提供了一个有效的解决方案，有助于提高测试效率和数据质量。未来的工作将进一步改进模型性能，扩展系统功能，并提供更友好的用户界面。</p>
            </div>
        </section>

        <!-- 快速导航 -->
        <section>
            <h2 class="text-2xl font-bold text-blue-700 mb-4 border-b pb-2">快速导航</h2>
            <div class="grid md:grid-cols-4 gap-4">
                <a href="data_overview.html" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition flex flex-col items-center text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/2906/2906274.png" alt="数据集" class="w-16 h-16 mb-4">
                    <h3 class="text-lg font-semibold text-blue-700">数据集说明</h3>
                    <p class="text-sm text-gray-600 mt-2">了解项目使用的数据集结构和特点</p>
                </a>
                <a href="model_training.html" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition flex flex-col items-center text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="模型构建训练" class="w-16 h-16 mb-4">
                    <h3 class="text-lg font-semibold text-blue-700">模型构建训练</h3>
                    <p class="text-sm text-gray-600 mt-2">查看模型构建训练过程和监控指标</p>
                </a>
                <a href="anomaly_detection.html" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition flex flex-col items-center text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/2910/2910824.png" alt="异常检测" class="w-16 h-16 mb-4">
                    <h3 class="text-lg font-semibold text-blue-700">异常检测</h3>
                    <p class="text-sm text-gray-600 mt-2">探索异常检测结果和分析</p>
                </a>
                <a href="technical_route.html" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition flex flex-col items-center text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/2299/2299297.png" alt="技术路线与展望" class="w-16 h-16 mb-4">
                    <h3 class="text-lg font-semibold text-blue-700">技术路线与展望</h3>
                    <p class="text-sm text-gray-600 mt-2">了解项目的技术路线、方法论和未来展望</p>
                </a>
                <!-- 价值分析链接已隐藏
                <a href="value_analysis.html" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition flex flex-col items-center text-center">
                    <img src="https://cdn-icons-png.flaticon.com/512/2343/2343505.png" alt="价值分析" class="w-16 h-16 mb-4">
                    <h3 class="text-lg font-semibold text-blue-700">价值分析</h3>
                    <p class="text-sm text-gray-600 mt-2">查看项目的经济效益和价值评估</p>
                </a>
                -->
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-blue-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-xl font-bold mb-2">天检中心-碰撞测试数据异常AI智能检测技术验证POC</h3>
                    <p class="text-blue-200">基于深度学习技术的数据异常检测解决方案</p>
                </div>
                <div class="text-center md:text-right">
                    <p class="mb-2">技术验证人/汇报人：刁国亮</p>
                    <p class="text-blue-200">邮箱: <EMAIL></p>
                </div>
            </div>
            <div class="border-t border-blue-700 mt-6 pt-6 text-center text-blue-200">
                <p>&copy; 2025 中汽数据（天津）有限公司. 保留所有权利.</p>
            </div>
        </div>
    </footer>



    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // 工作日志显示/隐藏功能
        function toggleWorkLog(contentId) {
            const content = document.getElementById(contentId);
            if (content.classList.contains('hidden')) {
                // 先隐藏所有工作日志内容
                document.querySelectorAll('[id$="Content"]').forEach(el => {
                    el.classList.add('hidden');
                });
                // 显示当前点击的工作日志内容
                content.classList.remove('hidden');
                // 更改按钮文本
                event.target.textContent = '收起详情 ↑';
            } else {
                // 隐藏当前工作日志内容
                content.classList.add('hidden');
                // 恢复按钮文本
                event.target.textContent = '查看详情 ↓';
            }
        }

        // 主要发现图表
        const findingsChart = echarts.init(document.getElementById('findings-chart'));
        const findingsOption = {
            title: {
                text: '异常百分比比较',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['原始数据集', '新数据集'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['最小值', '平均值', '最大值', '标准差']
            },
            yAxis: {
                type: 'value',
                name: '百分比 (%)'
            },
            series: [
                {
                    name: '原始数据集',
                    type: 'bar',
                    data: [0, 5.05, 11.60, 3.69],
                    itemStyle: {
                        color: '#3b82f6'
                    }
                },
                {
                    name: '新数据集',
                    type: 'bar',
                    data: [4.54, 4.84, 5.45, 0.52],
                    itemStyle: {
                        color: '#10b981'
                    }
                }
            ]
        };
        findingsChart.setOption(findingsOption);

        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            findingsChart.resize();
        });

    </script>
</body>
</html>
