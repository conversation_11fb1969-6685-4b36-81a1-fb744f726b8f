# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 通道相似性科学分析脚本
# 使用多种科学方法（时域特征、频域特征、DTW动态时间规整）评估不同传感器通道间的相似性，
# 为通用模型训练提供科学依据。包含层次聚类分析和可视化功能，
# 帮助确定哪些通道可以共享同一个异常检测模型。

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.spatial.distance import pdist, squareform
from scipy.fft import fft, fftfreq
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans, AgglomerativeClustering
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from dtaidistance import dtw
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class ChannelSimilarityAnalyzer:
    """通道相似性分析器"""

    def __init__(self, data_file, output_dir):
        self.data_file = data_file
        self.output_dir = output_dir
        self.multi_channel_data = {}
        self.similarity_results = {}

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def load_data(self):
        """加载多通道数据"""
        with open(self.data_file, 'r', encoding='utf-8') as f:
            self.multi_channel_data = json.load(f)
        print(f"加载了 {len(self.multi_channel_data)} 个数据集的多通道数据")

    def calculate_time_domain_similarity(self):
        """计算时域相似性"""
        print("计算时域相似性...")

        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        # 收集所有通道的时域特征
        channel_features = {ch: [] for ch in channels}

        for dataset_name, dataset in self.multi_channel_data.items():
            for channel in channels:
                if channel in dataset:
                    data = np.array(dataset[channel]['data'])
                    # 使用前1000个数据点
                    data_subset = data[:1000] if len(data) > 1000 else data

                    # 计算时域特征
                    features = [
                        np.mean(data_subset),           # 均值
                        np.std(data_subset),            # 标准差
                        np.max(data_subset),            # 最大值
                        np.min(data_subset),            # 最小值
                        np.ptp(data_subset),            # 峰峰值
                        stats.skew(data_subset),        # 偏度
                        stats.kurtosis(data_subset),    # 峰度
                        np.sqrt(np.mean(data_subset**2)), # RMS
                        np.mean(np.abs(data_subset)),   # 平均绝对值
                        len(np.where(np.diff(np.signbit(data_subset)))[0]) # 过零点数
                    ]
                    channel_features[channel].append(features)

        # 计算通道间相似性
        similarity_matrix = np.zeros((len(channels), len(channels)))

        for i, ch1 in enumerate(channels):
            for j, ch2 in enumerate(channels):
                if i <= j:
                    if channel_features[ch1] and channel_features[ch2]:
                        # 计算特征向量间的相关系数
                        features1 = np.array(channel_features[ch1])
                        features2 = np.array(channel_features[ch2])

                        # 计算每个特征维度的相关性，然后取平均
                        correlations = []
                        for k in range(features1.shape[1]):
                            if np.std(features1[:, k]) > 0 and np.std(features2[:, k]) > 0:
                                corr = np.corrcoef(features1[:, k], features2[:, k])[0, 1]
                                if not np.isnan(corr):
                                    correlations.append(abs(corr))

                        similarity = np.mean(correlations) if correlations else 0
                        similarity_matrix[i, j] = similarity
                        similarity_matrix[j, i] = similarity

        self.similarity_results['time_domain'] = {
            'matrix': similarity_matrix,
            'channels': channels,
            'features': channel_features
        }

        return similarity_matrix, channels

    def calculate_frequency_domain_similarity(self):
        """计算频域相似性"""
        print("计算频域相似性...")

        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        # 收集所有通道的频域特征
        channel_freq_features = {ch: [] for ch in channels}

        for dataset_name, dataset in self.multi_channel_data.items():
            for channel in channels:
                if channel in dataset:
                    data = np.array(dataset[channel]['data'])
                    data_subset = data[:1000] if len(data) > 1000 else data

                    # FFT分析
                    fft_data = fft(data_subset)
                    freqs = fftfreq(len(data_subset), d=0.0001)  # 采样间隔0.0001s

                    # 只取正频率部分
                    positive_freqs = freqs[:len(freqs)//2]
                    magnitude = np.abs(fft_data[:len(fft_data)//2])

                    # 计算频域特征
                    features = [
                        np.mean(magnitude),                    # 平均幅值
                        np.std(magnitude),                     # 幅值标准差
                        np.max(magnitude),                     # 最大幅值
                        np.argmax(magnitude) * (freqs[1] - freqs[0]),  # 主频率
                        np.sum(magnitude * positive_freqs) / np.sum(magnitude),  # 重心频率
                        np.sqrt(np.sum(magnitude * positive_freqs**2) / np.sum(magnitude)),  # RMS频率
                        np.sum(magnitude[:len(magnitude)//4]) / np.sum(magnitude),  # 低频能量比
                        np.sum(magnitude[len(magnitude)//2:]) / np.sum(magnitude),  # 高频能量比
                    ]
                    channel_freq_features[channel].append(features)

        # 计算频域相似性矩阵
        freq_similarity_matrix = np.zeros((len(channels), len(channels)))

        for i, ch1 in enumerate(channels):
            for j, ch2 in enumerate(channels):
                if i <= j:
                    if channel_freq_features[ch1] and channel_freq_features[ch2]:
                        features1 = np.array(channel_freq_features[ch1])
                        features2 = np.array(channel_freq_features[ch2])

                        correlations = []
                        for k in range(features1.shape[1]):
                            if np.std(features1[:, k]) > 0 and np.std(features2[:, k]) > 0:
                                corr = np.corrcoef(features1[:, k], features2[:, k])[0, 1]
                                if not np.isnan(corr):
                                    correlations.append(abs(corr))

                        similarity = np.mean(correlations) if correlations else 0
                        freq_similarity_matrix[i, j] = similarity
                        freq_similarity_matrix[j, i] = similarity

        self.similarity_results['frequency_domain'] = {
            'matrix': freq_similarity_matrix,
            'channels': channels,
            'features': channel_freq_features
        }

        return freq_similarity_matrix, channels

    def calculate_dtw_similarity(self):
        """计算动态时间规整(DTW)相似性"""
        print("计算DTW相似性...")

        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        # 收集每个通道的代表性时序数据
        channel_representative_data = {}

        for channel in channels:
            channel_data = []
            for dataset_name, dataset in self.multi_channel_data.items():
                if channel in dataset:
                    data = np.array(dataset[channel]['data'])
                    data_subset = data[:500] if len(data) > 500 else data  # 使用较短序列以提高计算效率
                    # 标准化
                    if np.std(data_subset) > 0:
                        data_subset = (data_subset - np.mean(data_subset)) / np.std(data_subset)
                    channel_data.append(data_subset)

            if channel_data:
                # 使用第一个数据集作为代表
                channel_representative_data[channel] = channel_data[0]

        # 计算DTW距离矩阵
        dtw_distance_matrix = np.zeros((len(channels), len(channels)))

        for i, ch1 in enumerate(channels):
            for j, ch2 in enumerate(channels):
                if i <= j:
                    if ch1 in channel_representative_data and ch2 in channel_representative_data:
                        try:
                            distance = dtw.distance(
                                channel_representative_data[ch1],
                                channel_representative_data[ch2]
                            )
                            dtw_distance_matrix[i, j] = distance
                            dtw_distance_matrix[j, i] = distance
                        except:
                            dtw_distance_matrix[i, j] = float('inf')
                            dtw_distance_matrix[j, i] = float('inf')

        # 转换为相似性矩阵（距离越小，相似性越高）
        max_distance = np.max(dtw_distance_matrix[dtw_distance_matrix != float('inf')])
        dtw_similarity_matrix = 1 - (dtw_distance_matrix / max_distance)
        dtw_similarity_matrix[dtw_distance_matrix == float('inf')] = 0

        self.similarity_results['dtw'] = {
            'matrix': dtw_similarity_matrix,
            'channels': channels,
            'distance_matrix': dtw_distance_matrix
        }

        return dtw_similarity_matrix, channels

    def perform_hierarchical_clustering(self):
        """执行层次聚类分析"""
        print("执行层次聚类分析...")

        # 综合相似性矩阵
        time_sim = self.similarity_results['time_domain']['matrix']
        freq_sim = self.similarity_results['frequency_domain']['matrix']
        dtw_sim = self.similarity_results['dtw']['matrix']

        # 加权平均
        combined_similarity = (time_sim * 0.4 + freq_sim * 0.4 + dtw_sim * 0.2)

        # 转换为距离矩阵
        distance_matrix = 1 - combined_similarity

        # 层次聚类
        clustering = AgglomerativeClustering(
            n_clusters=3,
            metric='precomputed',
            linkage='average'
        )

        cluster_labels = clustering.fit_predict(distance_matrix)

        channels = self.similarity_results['time_domain']['channels']

        # 保存聚类结果
        self.similarity_results['clustering'] = {
            'labels': cluster_labels,
            'channels': channels,
            'combined_similarity': combined_similarity
        }

        return cluster_labels, channels, combined_similarity

    def create_similarity_visualizations(self):
        """创建相似性可视化图表"""
        print("生成相似性可视化图表...")

        # 1. 相似性矩阵热图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('通道相似性分析', fontsize=16, fontweight='bold')

        channels = self.similarity_results['time_domain']['channels']

        # 时域相似性
        sns.heatmap(self.similarity_results['time_domain']['matrix'],
                   annot=True, fmt='.3f', cmap='Blues',
                   xticklabels=channels, yticklabels=channels, ax=axes[0,0])
        axes[0,0].set_title('时域特征相似性')

        # 频域相似性
        sns.heatmap(self.similarity_results['frequency_domain']['matrix'],
                   annot=True, fmt='.3f', cmap='Greens',
                   xticklabels=channels, yticklabels=channels, ax=axes[0,1])
        axes[0,1].set_title('频域特征相似性')

        # DTW相似性
        sns.heatmap(self.similarity_results['dtw']['matrix'],
                   annot=True, fmt='.3f', cmap='Oranges',
                   xticklabels=channels, yticklabels=channels, ax=axes[1,0])
        axes[1,0].set_title('DTW时序相似性')

        # 综合相似性
        sns.heatmap(self.similarity_results['clustering']['combined_similarity'],
                   annot=True, fmt='.3f', cmap='Reds',
                   xticklabels=channels, yticklabels=channels, ax=axes[1,1])
        axes[1,1].set_title('综合相似性')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'similarity_matrices.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 聚类结果可视化
        self._create_clustering_visualization()

    def _create_clustering_visualization(self):
        """创建聚类结果可视化"""
        cluster_labels = self.similarity_results['clustering']['labels']
        channels = self.similarity_results['clustering']['channels']

        # 创建聚类结果图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 聚类树状图
        from scipy.cluster.hierarchy import dendrogram, linkage
        distance_matrix = 1 - self.similarity_results['clustering']['combined_similarity']

        # 转换为压缩距离矩阵格式
        condensed_distances = squareform(distance_matrix)
        linkage_matrix = linkage(condensed_distances, method='average')

        dendrogram(linkage_matrix, labels=channels, ax=ax1, orientation='top')
        ax1.set_title('通道层次聚类树状图', fontweight='bold')
        ax1.tick_params(axis='x', rotation=45)

        # 聚类结果散点图（使用PCA降维）
        pca = PCA(n_components=2)
        similarity_2d = pca.fit_transform(self.similarity_results['clustering']['combined_similarity'])

        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i, cluster in enumerate(set(cluster_labels)):
            mask = cluster_labels == cluster
            ax2.scatter(similarity_2d[mask, 0], similarity_2d[mask, 1],
                       c=colors[i], label=f'聚类 {cluster}', s=100, alpha=0.7)

            # 添加标签
            for j, channel in enumerate(np.array(channels)[mask]):
                ax2.annotate(channel.split()[-1],
                           (similarity_2d[mask][j-sum(~mask[:j]), 0],
                            similarity_2d[mask][j-sum(~mask[:j]), 1]),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

        ax2.set_title('通道聚类结果 (PCA降维)', fontweight='bold')
        ax2.set_xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
        ax2.set_ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'clustering_results.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def save_analysis_results(self):
        """保存分析结果"""
        # 转换numpy数组为列表以便JSON序列化
        serializable_results = {}

        for analysis_type, results in self.similarity_results.items():
            serializable_results[analysis_type] = {}
            for key, value in results.items():
                if isinstance(value, np.ndarray):
                    serializable_results[analysis_type][key] = value.tolist()
                else:
                    serializable_results[analysis_type][key] = value

        # 保存到JSON文件
        with open(os.path.join(self.output_dir, 'similarity_analysis_results.json'), 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"相似性分析结果已保存到 {self.output_dir}")

def main():
    """主函数"""
    data_file = "results/multi_channel_analysis/multi_channel_data.json"
    output_dir = "results/channel_similarity_analysis"

    # 创建分析器
    analyzer = ChannelSimilarityAnalyzer(data_file, output_dir)

    # 执行分析
    analyzer.load_data()

    # 计算各种相似性
    time_sim, channels = analyzer.calculate_time_domain_similarity()
    freq_sim, _ = analyzer.calculate_frequency_domain_similarity()
    dtw_sim, _ = analyzer.calculate_dtw_similarity()

    # 执行聚类分析
    cluster_labels, channels, combined_sim = analyzer.perform_hierarchical_clustering()

    # 创建可视化
    analyzer.create_similarity_visualizations()

    # 保存结果
    analyzer.save_analysis_results()

    # 打印结果
    print("\n=== 通道相似性分析结果 ===")
    print("聚类结果:")
    for i, (channel, cluster) in enumerate(zip(channels, cluster_labels)):
        print(f"{channel}: 聚类 {cluster}")

    print(f"\n分析完成，结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
