# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多通道数据可视化生成器脚本
用于创建展示多通道碰撞测试数据特征和相似性分析的可视化图表。
包含时序数据对比、特征分布分析、通道相关性矩阵和聚类分析功能，
为多通道数据理解和模型训练策略制定提供可视化支持。
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.spatial.distance import pdist, squareform
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import pandas as pd
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class MultiChannelVisualizer:
    """多通道数据可视化器"""

    def __init__(self, data_file, output_dir):
        self.data_file = data_file
        self.output_dir = output_dir
        self.multi_channel_data = {}
        self.channel_features = {}

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def load_data(self):
        """加载多通道数据"""
        with open(self.data_file, 'r', encoding='utf-8') as f:
            self.multi_channel_data = json.load(f)
        print(f"加载了 {len(self.multi_channel_data)} 个数据集的多通道数据")

    def calculate_channel_features(self):
        """计算各通道的统计特征"""
        print("计算通道特征...")

        # 获取所有通道名称
        all_channels = set()
        for dataset in self.multi_channel_data.values():
            all_channels.update(dataset.keys())

        self.channel_features = {channel: [] for channel in all_channels}

        # 为每个数据集的每个通道计算特征
        for dataset_name, dataset in self.multi_channel_data.items():
            for channel_name, channel_info in dataset.items():
                data = np.array(channel_info['data'])

                # 计算统计特征
                features = {
                    'dataset': dataset_name,
                    'mean': np.mean(data),
                    'std': np.std(data),
                    'min': np.min(data),
                    'max': np.max(data),
                    'range': np.max(data) - np.min(data),
                    'skewness': stats.skew(data),
                    'kurtosis': stats.kurtosis(data),
                    'rms': np.sqrt(np.mean(data**2)),
                    'peak_to_peak': np.ptp(data),
                    'zero_crossings': len(np.where(np.diff(np.signbit(data)))[0]),
                    'data_length': len(data)
                }

                self.channel_features[channel_name].append(features)

        print("特征计算完成")

    def create_channel_comparison_plots(self):
        """创建通道对比图"""
        print("生成通道对比图...")

        # 1. 时序数据对比图
        fig, axes = plt.subplots(3, 2, figsize=(15, 12))
        fig.suptitle('多通道时序数据对比分析', fontsize=16, fontweight='bold')

        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        for i, channel in enumerate(channels):
            row, col = i // 2, i % 2
            ax = axes[row, col]

            # 绘制前5个数据集的该通道数据
            dataset_count = 0
            for dataset_name, dataset in self.multi_channel_data.items():
                if dataset_count >= 5:
                    break
                if channel in dataset:
                    data = np.array(dataset[channel]['data'])
                    # 只显示前1000个数据点以提高可读性
                    display_data = data[:1000] if len(data) > 1000 else data
                    time_axis = np.arange(len(display_data)) * 0.0001  # 采样间隔0.0001s
                    ax.plot(time_axis, display_data, alpha=0.7, label=f'Dataset {dataset_count+1}')
                    dataset_count += 1

            ax.set_title(f'{channel}', fontweight='bold')
            ax.set_xlabel('时间 (s)')
            ax.set_ylabel('加速度 (m/s²)')
            ax.grid(True, alpha=0.3)
            ax.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'channel_comparison.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 通道特征分布图
        self._create_feature_distribution_plots()

        # 3. 通道相关性矩阵
        self._create_correlation_matrix()

    def _create_feature_distribution_plots(self):
        """创建特征分布图"""
        features_to_plot = ['mean', 'std', 'range', 'rms']

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('通道特征分布对比', fontsize=16, fontweight='bold')

        for i, feature in enumerate(features_to_plot):
            row, col = i // 2, i % 2
            ax = axes[row, col]

            # 收集所有通道的该特征数据
            feature_data = {}
            for channel_name, channel_features in self.channel_features.items():
                if channel_features:  # 确保有数据
                    feature_values = [f[feature] for f in channel_features]
                    feature_data[channel_name] = feature_values

            # 创建箱线图
            if feature_data:
                channels = list(feature_data.keys())[:6]  # 只显示前6个通道
                data_for_plot = [feature_data[ch] for ch in channels]

                bp = ax.boxplot(data_for_plot, labels=channels, patch_artist=True)

                # 设置颜色
                colors = plt.cm.Set3(np.linspace(0, 1, len(channels)))
                for patch, color in zip(bp['boxes'], colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)

            ax.set_title(f'{feature.upper()} 分布', fontweight='bold')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'feature_distribution.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def _create_correlation_matrix(self):
        """创建通道间相关性矩阵"""
        print("计算通道间相关性...")

        # 准备数据矩阵
        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        correlation_data = []
        dataset_names = []

        for dataset_name, dataset in self.multi_channel_data.items():
            if all(ch in dataset for ch in channels):
                row = []
                for channel in channels:
                    data = np.array(dataset[channel]['data'])
                    # 使用前1000个数据点计算特征
                    data_subset = data[:1000] if len(data) > 1000 else data
                    row.extend([np.mean(data_subset), np.std(data_subset), np.max(data_subset)])
                correlation_data.append(row)
                dataset_names.append(dataset_name)

        if correlation_data:
            correlation_matrix = np.corrcoef(np.array(correlation_data).T)

            # 创建特征标签
            feature_labels = []
            for channel in channels:
                feature_labels.extend([f'{channel}_mean', f'{channel}_std', f'{channel}_max'])

            # 绘制相关性热图
            plt.figure(figsize=(12, 10))
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                       square=True, fmt='.2f', cbar_kws={"shrink": .8})
            plt.title('通道特征间相关性矩阵', fontsize=16, fontweight='bold')
            plt.xticks(range(len(feature_labels)), feature_labels, rotation=45, ha='right')
            plt.yticks(range(len(feature_labels)), feature_labels, rotation=0)
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, 'images', 'correlation_matrix.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    def perform_clustering_analysis(self):
        """执行聚类分析"""
        print("执行聚类分析...")

        # 准备聚类数据
        channels = ['Head Acceleration X', 'Head Acceleration Y', 'Head Acceleration Z',
                   'Chest Acceleration X', 'Chest Acceleration Y', 'Chest Acceleration Z']

        clustering_data = []
        dataset_labels = []

        for dataset_name, dataset in self.multi_channel_data.items():
            if all(ch in dataset for ch in channels):
                row = []
                for channel in channels:
                    data = np.array(dataset[channel]['data'])
                    data_subset = data[:1000] if len(data) > 1000 else data
                    # 计算多个统计特征
                    row.extend([
                        np.mean(data_subset),
                        np.std(data_subset),
                        np.max(data_subset),
                        np.min(data_subset),
                        stats.skew(data_subset),
                        stats.kurtosis(data_subset)
                    ])
                clustering_data.append(row)
                dataset_labels.append(dataset_name)

        if len(clustering_data) > 3:
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(clustering_data)

            # K-means聚类
            kmeans = KMeans(n_clusters=3, random_state=42)
            cluster_labels = kmeans.fit_predict(scaled_data)

            # PCA降维可视化
            pca = PCA(n_components=2)
            pca_data = pca.fit_transform(scaled_data)

            # 绘制聚类结果
            plt.figure(figsize=(10, 8))
            scatter = plt.scatter(pca_data[:, 0], pca_data[:, 1], c=cluster_labels,
                                cmap='viridis', alpha=0.7, s=100)
            plt.colorbar(scatter)
            plt.title('数据集聚类分析 (PCA降维可视化)', fontsize=16, fontweight='bold')
            plt.xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
            plt.ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')

            # 添加数据点标签
            for i, label in enumerate(dataset_labels):
                plt.annotate(label[-4:], (pca_data[i, 0], pca_data[i, 1]),
                           xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.7)

            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, 'images', 'clustering_analysis.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

            return cluster_labels, dataset_labels

        return None, None

def main():
    """主函数"""
    data_file = "results/multi_channel_analysis/multi_channel_data.json"
    output_dir = "results/multi_channel_visualization"

    # 创建可视化器
    visualizer = MultiChannelVisualizer(data_file, output_dir)

    # 执行分析
    visualizer.load_data()
    visualizer.calculate_channel_features()
    visualizer.create_channel_comparison_plots()
    cluster_labels, dataset_labels = visualizer.perform_clustering_analysis()

    print(f"可视化结果已保存到 {output_dir}")

    if cluster_labels is not None:
        print("\n=== 聚类分析结果 ===")
        for i, (dataset, cluster) in enumerate(zip(dataset_labels, cluster_labels)):
            print(f"{dataset}: 聚类 {cluster}")

if __name__ == "__main__":
    main()
