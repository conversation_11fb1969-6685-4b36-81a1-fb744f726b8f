# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
客户试验项目通道数分析脚本
基于客户提供的试验项目各通道数Excel文件进行深入分析，
统计不同标准、不同位置的通道数分布情况，
为通用模型训练策略和成本分析提供数据支持。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class CustomerProjectAnalyzer:
    """客户试验项目分析器"""

    def __init__(self, excel_file, output_dir):
        self.excel_file = excel_file
        self.output_dir = output_dir
        self.df = None
        self.analysis_results = {}

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def load_data(self):
        """加载Excel数据"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"成功加载数据，共 {len(self.df)} 行，{len(self.df.columns)} 列")

            # 清理数据
            self.df = self.df.dropna(subset=['碰撞试验项目'])  # 移除没有项目名称的行
            print(f"清理后数据，共 {len(self.df)} 个有效试验项目")

        except Exception as e:
            print(f"加载数据时出错: {e}")
            return False
        return True

    def analyze_project_types(self):
        """分析试验项目类型"""
        print("分析试验项目类型...")

        # 按标准分组统计
        standard_stats = self.df.groupby('标准').agg({
            '碰撞试验项目': 'count',
            '总计': 'sum'
        }).rename(columns={'碰撞试验项目': '项目数量', '总计': '总通道数'})

        # 按试验项目类型统计
        project_stats = self.df.groupby('碰撞试验项目').agg({
            '序号': 'count',
            '总计': ['sum', 'mean', 'std']
        })
        project_stats.columns = ['项目数量', '总通道数', '平均通道数', '通道数标准差']

        # 按位置统计
        position_stats = self.df[['POS1', 'POS3', 'POS4', 'POS6']].describe()

        self.analysis_results['standard_stats'] = standard_stats
        self.analysis_results['project_stats'] = project_stats
        self.analysis_results['position_stats'] = position_stats

        return standard_stats, project_stats, position_stats

    def analyze_channel_distribution(self):
        """分析通道数分布"""
        print("分析通道数分布...")

        # 总通道数分布
        total_channels = self.df['总计'].dropna()

        # 各位置通道数分布
        pos_data = {
            'POS1': self.df['POS1'].dropna(),
            'POS3': self.df['POS3'].dropna(),
            'POS4': self.df['POS4'].dropna(),
            'POS6': pd.to_numeric(self.df['POS6'], errors='coerce').dropna()
        }

        # 计算统计信息
        channel_distribution = {
            'total_channels_stats': {
                'mean': total_channels.mean(),
                'median': total_channels.median(),
                'std': total_channels.std(),
                'min': total_channels.min(),
                'max': total_channels.max(),
                'total_sum': total_channels.sum()
            },
            'position_stats': {}
        }

        for pos, data in pos_data.items():
            if len(data) > 0:
                channel_distribution['position_stats'][pos] = {
                    'mean': data.mean(),
                    'median': data.median(),
                    'std': data.std(),
                    'min': data.min(),
                    'max': data.max(),
                    'total_sum': data.sum()
                }

        self.analysis_results['channel_distribution'] = channel_distribution
        return channel_distribution

    def create_visualizations(self):
        """创建可视化图表"""
        print("生成可视化图表...")

        # 1. 试验项目类型分布
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('客户试验项目通道数分析', fontsize=16, fontweight='bold')

        # 按标准分组的项目数量
        standard_counts = self.df['标准'].value_counts()
        axes[0,0].pie(standard_counts.values, labels=standard_counts.index, autopct='%1.1f%%')
        axes[0,0].set_title('按标准分组的项目分布')

        # 试验项目类型分布
        project_counts = self.df['碰撞试验项目'].value_counts()
        axes[0,1].barh(range(len(project_counts)), project_counts.values)
        axes[0,1].set_yticks(range(len(project_counts)))
        axes[0,1].set_yticklabels(project_counts.index)
        axes[0,1].set_title('试验项目类型分布')
        axes[0,1].set_xlabel('项目数量')

        # 总通道数分布
        total_channels = self.df['总计'].dropna()
        axes[1,0].hist(total_channels, bins=15, alpha=0.7, edgecolor='black')
        axes[1,0].set_title('总通道数分布')
        axes[1,0].set_xlabel('通道数')
        axes[1,0].set_ylabel('项目数量')
        axes[1,0].axvline(total_channels.mean(), color='red', linestyle='--',
                         label=f'平均值: {total_channels.mean():.1f}')
        axes[1,0].legend()

        # 各位置通道数对比
        pos_data = []
        pos_labels = []
        for pos in ['POS1', 'POS3', 'POS4']:
            data = self.df[pos].dropna()
            if len(data) > 0:
                pos_data.append(data)
                pos_labels.append(pos)

        if pos_data:
            axes[1,1].boxplot(pos_data, labels=pos_labels)
            axes[1,1].set_title('各位置通道数分布')
            axes[1,1].set_ylabel('通道数')
            axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'project_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 详细的通道数分析
        self._create_detailed_channel_analysis()

    def _create_detailed_channel_analysis(self):
        """创建详细的通道数分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('详细通道数分析', fontsize=16, fontweight='bold')

        # 各标准的通道数对比
        standards = self.df['标准'].dropna().unique()
        standard_channels = []
        standard_labels = []

        for std in standards:
            std_data = self.df[self.df['标准'] == std]['总计'].dropna()
            if len(std_data) > 0:
                standard_channels.append(std_data)
                standard_labels.append(std)

        if standard_channels:
            axes[0,0].boxplot(standard_channels, labels=standard_labels)
            axes[0,0].set_title('各标准通道数分布')
            axes[0,0].set_ylabel('通道数')
            axes[0,0].tick_params(axis='x', rotation=45)

        # 项目类型vs通道数散点图
        project_channel_data = self.df.groupby('碰撞试验项目')['总计'].agg(['mean', 'count']).reset_index()
        scatter = axes[0,1].scatter(project_channel_data['count'], project_channel_data['mean'],
                                   s=100, alpha=0.7)
        axes[0,1].set_xlabel('项目数量')
        axes[0,1].set_ylabel('平均通道数')
        axes[0,1].set_title('项目类型 vs 通道数关系')

        # 添加标签
        for i, row in project_channel_data.iterrows():
            axes[0,1].annotate(row['碰撞试验项目'],
                              (row['count'], row['mean']),
                              xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 位置通道数堆叠图
        pos_columns = ['POS1', 'POS3', 'POS4']
        pos_data_for_stack = self.df[pos_columns].fillna(0)

        # 选择前10个项目进行展示
        top_projects = self.df.head(10)
        pos_data_subset = top_projects[pos_columns].fillna(0)

        bottom = np.zeros(len(pos_data_subset))
        colors = ['#ff9999', '#66b3ff', '#99ff99']

        for i, pos in enumerate(pos_columns):
            axes[1,0].bar(range(len(pos_data_subset)), pos_data_subset[pos],
                         bottom=bottom, label=pos, color=colors[i], alpha=0.8)
            bottom += pos_data_subset[pos]

        axes[1,0].set_title('前10个项目的位置通道数分布')
        axes[1,0].set_xlabel('项目序号')
        axes[1,0].set_ylabel('通道数')
        axes[1,0].legend()
        axes[1,0].set_xticks(range(len(pos_data_subset)))
        axes[1,0].set_xticklabels([f"项目{i+1}" for i in range(len(pos_data_subset))], rotation=45)

        # 通道数累积分布
        total_channels_sorted = self.df['总计'].dropna().sort_values()
        cumulative_pct = np.arange(1, len(total_channels_sorted) + 1) / len(total_channels_sorted) * 100

        axes[1,1].plot(total_channels_sorted, cumulative_pct, marker='o', markersize=4)
        axes[1,1].set_xlabel('通道数')
        axes[1,1].set_ylabel('累积百分比 (%)')
        axes[1,1].set_title('通道数累积分布')
        axes[1,1].grid(True, alpha=0.3)

        # 添加关键百分位数标记
        percentiles = [25, 50, 75, 90]
        for p in percentiles:
            value = np.percentile(total_channels_sorted, p)
            axes[1,1].axvline(value, color='red', linestyle='--', alpha=0.7)
            axes[1,1].text(value, p, f'P{p}: {value:.0f}', rotation=90,
                          verticalalignment='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'detailed_channel_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def generate_insights(self):
        """生成分析洞察"""
        print("生成分析洞察...")

        insights = {
            'key_findings': [],
            'recommendations': [],
            'statistics': {}
        }

        # 关键发现
        total_projects = len(self.df)
        total_channels_sum = self.df['总计'].sum()
        avg_channels = self.df['总计'].mean()

        insights['key_findings'].extend([
            f"共有 {total_projects} 个不同的试验项目类型",
            f"总计需要 {total_channels_sum} 个数据通道",
            f"平均每个项目需要 {avg_channels:.1f} 个通道",
            f"通道数范围从 {self.df['总计'].min()} 到 {self.df['总计'].max()}"
        ])

        # 标准分析
        standard_analysis = self.df.groupby('标准').agg({
            '碰撞试验项目': 'count',
            '总计': ['sum', 'mean']
        })

        for standard in standard_analysis.index:
            if pd.notna(standard):
                project_count = standard_analysis.loc[standard, ('碰撞试验项目', 'count')]
                total_channels = standard_analysis.loc[standard, ('总计', 'sum')]
                avg_channels = standard_analysis.loc[standard, ('总计', 'mean')]

                insights['key_findings'].append(
                    f"{standard}标准: {project_count}个项目, 共{total_channels}个通道, 平均{avg_channels:.1f}个/项目"
                )

        # 位置分析
        pos_stats = {}
        for pos in ['POS1', 'POS3', 'POS4']:
            pos_data = self.df[pos].dropna()
            if len(pos_data) > 0:
                pos_stats[pos] = {
                    'total': pos_data.sum(),
                    'avg': pos_data.mean(),
                    'projects': len(pos_data)
                }

        insights['key_findings'].append("位置分布分析:")
        for pos, stats in pos_stats.items():
            insights['key_findings'].append(
                f"  {pos}: {stats['projects']}个项目使用, 共{stats['total']}个通道, 平均{stats['avg']:.1f}个/项目"
            )

        # 建议
        insights['recommendations'].extend([
            "基于通道数分布，建议优先开发POS1位置的通用模型（使用最广泛）",
            "考虑按标准分组开发专用模型，不同标准的通道配置差异较大",
            "对于高频使用的试验项目类型（如正碰、侧碰），优先开发通用模型",
            f"总计{total_channels_sum}个通道的训练成本很高，通用模型方案可显著降低成本"
        ])

        insights['statistics'] = {
            'total_projects': total_projects,
            'total_channels': int(total_channels_sum),
            'avg_channels_per_project': round(avg_channels, 1),
            'min_channels': int(self.df['总计'].min()),
            'max_channels': int(self.df['总计'].max()),
            'position_stats': pos_stats
        }

        self.analysis_results['insights'] = insights
        return insights

    def save_results(self):
        """保存分析结果"""
        import json

        def convert_to_serializable(obj):
            """递归转换对象为JSON可序列化格式"""
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict()
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif pd.isna(obj):
                return None
            else:
                return obj

        # 转换为可序列化格式
        serializable_results = convert_to_serializable(self.analysis_results)

        # 保存到JSON文件
        with open(os.path.join(self.output_dir, 'customer_project_analysis.json'), 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"分析结果已保存到 {self.output_dir}")

def main():
    """主函数"""
    excel_file = "docs/试验项目各通道数.xlsx"
    output_dir = "results/customer_project_analysis"

    # 创建分析器
    analyzer = CustomerProjectAnalyzer(excel_file, output_dir)

    # 执行分析
    if analyzer.load_data():
        analyzer.analyze_project_types()
        analyzer.analyze_channel_distribution()
        analyzer.create_visualizations()
        insights = analyzer.generate_insights()
        analyzer.save_results()

        # 打印关键洞察
        print("\n" + "="*60)
        print("客户试验项目分析结果")
        print("="*60)

        print("\n关键发现:")
        for finding in insights['key_findings']:
            print(f"• {finding}")

        print("\n建议:")
        for rec in insights['recommendations']:
            print(f"• {rec}")

        print(f"\n分析完成，详细结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
