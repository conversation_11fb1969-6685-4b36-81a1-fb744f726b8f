# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
力方向性聚类分析脚本
基于碰撞力学原理，按照力的方向性对传感器进行聚类分析，
分析同方向不同位置传感器的数据相似性，
为基于物理原理的模型聚类策略提供科学依据。
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.spatial.distance import pdist, squareform
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class DirectionalForceAnalyzer:
    """力方向性分析器"""

    def __init__(self, data_file, channel_metadata_file, output_dir):
        self.data_file = data_file
        self.channel_metadata_file = channel_metadata_file
        self.output_dir = output_dir
        self.multi_channel_data = {}
        self.channel_metadata = {}
        self.directional_analysis = {}

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def load_data(self):
        """加载多通道数据和元数据"""
        # 加载多通道数据
        with open(self.data_file, 'r', encoding='utf-8') as f:
            self.multi_channel_data = json.load(f)

        # 加载通道元数据
        with open(self.channel_metadata_file, 'r', encoding='utf-8') as f:
            self.channel_metadata = json.load(f)

        print(f"加载了 {len(self.multi_channel_data)} 个数据集的多通道数据")
        print(f"加载了 {len(self.channel_metadata)} 个通道的元数据")

    def extract_directional_channels(self):
        """提取方向性通道数据"""
        print("提取方向性通道数据...")

        # 定义方向性通道模式
        directional_patterns = {
            'X_direction': {
                'acceleration': ['Head Acceleration X', 'Chest Acceleration X', 'Pelvis Acceleration X'],
                'force': ['Neck Upper Force X', 'Femur Left Force X', 'Femur Right Force X'],
                'moment': ['Neck Upper Moment X']
            },
            'Y_direction': {
                'acceleration': ['Head Acceleration Y', 'Chest Acceleration Y', 'Pelvis Acceleration Y'],
                'force': ['Neck Upper Force Y', 'Femur Left Force Y', 'Femur Right Force Y'],
                'moment': ['Neck Upper Moment Y']
            },
            'Z_direction': {
                'acceleration': ['Head Acceleration Z', 'Chest Acceleration Z', 'Pelvis Acceleration Z'],
                'force': ['Neck Upper Force Z', 'Femur Left Force Z', 'Femur Right Force Z'],
                'moment': ['Neck Upper Moment Z']
            }
        }

        # 提取实际存在的方向性通道
        available_directional_channels = {}

        for direction, measurement_types in directional_patterns.items():
            available_directional_channels[direction] = {}

            for measurement_type, channels in measurement_types.items():
                available_channels = []
                for channel in channels:
                    if channel in self.channel_metadata:
                        available_channels.append(channel)

                if available_channels:
                    available_directional_channels[direction][measurement_type] = available_channels

        self.directional_analysis['channel_mapping'] = available_directional_channels

        # 统计方向性通道分布
        direction_stats = {}
        for direction, measurement_types in available_directional_channels.items():
            total_channels = sum(len(channels) for channels in measurement_types.values())
            direction_stats[direction] = {
                'total_channels': total_channels,
                'measurement_types': list(measurement_types.keys()),
                'channels_by_type': {mt: len(channels) for mt, channels in measurement_types.items()}
            }

        self.directional_analysis['direction_stats'] = direction_stats

        print("方向性通道分布:")
        for direction, stats in direction_stats.items():
            print(f"  {direction}: {stats['total_channels']}个通道")
            for mt, count in stats['channels_by_type'].items():
                print(f"    {mt}: {count}个")

        return available_directional_channels

    def analyze_cross_position_similarity(self):
        """分析同方向跨位置的相似性"""
        print("分析同方向跨位置相似性...")

        directional_channels = self.directional_analysis['channel_mapping']
        cross_position_similarity = {}

        for direction in ['X_direction', 'Y_direction', 'Z_direction']:
            if direction not in directional_channels:
                continue

            direction_similarity = {}

            # 分析加速度通道的跨位置相似性
            if 'acceleration' in directional_channels[direction]:
                acc_channels = directional_channels[direction]['acceleration']

                if len(acc_channels) >= 2:
                    # 提取加速度数据
                    acc_data_matrix = []
                    acc_channel_names = []

                    for channel in acc_channels:
                        channel_features = []
                        for dataset_name, dataset in self.multi_channel_data.items():
                            if channel in dataset:
                                data = np.array(dataset[channel]['data'])
                                data_subset = data[:1000] if len(data) > 1000 else data

                                # 计算特征
                                features = [
                                    np.mean(data_subset),
                                    np.std(data_subset),
                                    np.max(data_subset),
                                    np.min(data_subset),
                                    stats.skew(data_subset),
                                    stats.kurtosis(data_subset),
                                    np.sqrt(np.mean(data_subset**2))  # RMS
                                ]
                                channel_features.extend(features)

                        if channel_features:
                            acc_data_matrix.append(channel_features)
                            acc_channel_names.append(channel)

                    # 计算相似性矩阵
                    if len(acc_data_matrix) >= 2:
                        similarity_matrix = np.corrcoef(acc_data_matrix)
                        direction_similarity['acceleration'] = {
                            'similarity_matrix': similarity_matrix.tolist(),
                            'channels': acc_channel_names,
                            'avg_similarity': np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
                        }

            # 分析力通道的跨位置相似性
            if 'force' in directional_channels[direction]:
                force_channels = directional_channels[direction]['force']

                if len(force_channels) >= 2:
                    # 类似的分析过程...
                    force_data_matrix = []
                    force_channel_names = []

                    for channel in force_channels:
                        channel_features = []
                        for dataset_name, dataset in self.multi_channel_data.items():
                            if channel in dataset:
                                data = np.array(dataset[channel]['data'])
                                data_subset = data[:1000] if len(data) > 1000 else data

                                features = [
                                    np.mean(data_subset),
                                    np.std(data_subset),
                                    np.max(data_subset),
                                    np.min(data_subset),
                                    stats.skew(data_subset),
                                    stats.kurtosis(data_subset),
                                    np.sqrt(np.mean(data_subset**2))
                                ]
                                channel_features.extend(features)

                        if channel_features:
                            force_data_matrix.append(channel_features)
                            force_channel_names.append(channel)

                    if len(force_data_matrix) >= 2:
                        similarity_matrix = np.corrcoef(force_data_matrix)
                        direction_similarity['force'] = {
                            'similarity_matrix': similarity_matrix.tolist(),
                            'channels': force_channel_names,
                            'avg_similarity': np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
                        }

            cross_position_similarity[direction] = direction_similarity

        self.directional_analysis['cross_position_similarity'] = cross_position_similarity
        return cross_position_similarity

    def analyze_directional_clustering(self):
        """执行方向性聚类分析"""
        print("执行方向性聚类分析...")

        # 收集所有方向性通道的特征数据
        all_directional_features = []
        channel_labels = []
        direction_labels = []
        measurement_labels = []

        directional_channels = self.directional_analysis['channel_mapping']

        for direction, measurement_types in directional_channels.items():
            for measurement_type, channels in measurement_types.items():
                for channel in channels:
                    # 提取通道特征
                    channel_features = []
                    feature_count = 0

                    for dataset_name, dataset in self.multi_channel_data.items():
                        if channel in dataset:
                            data = np.array(dataset[channel]['data'])
                            data_subset = data[:1000] if len(data) > 1000 else data

                            # 计算多维特征
                            features = [
                                np.mean(data_subset),
                                np.std(data_subset),
                                np.max(data_subset),
                                np.min(data_subset),
                                np.ptp(data_subset),  # 峰峰值
                                stats.skew(data_subset),
                                stats.kurtosis(data_subset),
                                np.sqrt(np.mean(data_subset**2)),  # RMS
                                np.mean(np.abs(data_subset)),  # 平均绝对值
                                len(np.where(np.diff(np.signbit(data_subset)))[0])  # 过零点数
                            ]
                            channel_features.extend(features)
                            feature_count += 1

                    if feature_count >= 3:  # 至少有3个数据集的数据
                        all_directional_features.append(channel_features)
                        channel_labels.append(channel)
                        direction_labels.append(direction)
                        measurement_labels.append(measurement_type)

        if len(all_directional_features) >= 4:
            # 标准化特征
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(all_directional_features)

            # K-means聚类（按方向分组）
            n_clusters = min(6, len(set(direction_labels)))  # 最多6个聚类
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(scaled_features)

            # 计算轮廓系数
            silhouette_avg = silhouette_score(scaled_features, cluster_labels)

            # PCA降维可视化
            pca = PCA(n_components=2)
            pca_features = pca.fit_transform(scaled_features)

            clustering_results = {
                'cluster_labels': cluster_labels.tolist(),
                'channel_labels': channel_labels,
                'direction_labels': direction_labels,
                'measurement_labels': measurement_labels,
                'silhouette_score': silhouette_avg,
                'pca_features': pca_features.tolist(),
                'pca_explained_variance': pca.explained_variance_ratio_.tolist()
            }

            self.directional_analysis['clustering_results'] = clustering_results

            print(f"聚类分析完成，轮廓系数: {silhouette_avg:.3f}")
            return clustering_results

        return None

    def create_directional_visualizations(self):
        """创建方向性分析可视化"""
        print("生成方向性分析可视化...")

        # 1. 方向性通道分布图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('力方向性聚类分析', fontsize=16, fontweight='bold')

        # 方向性通道数量分布
        direction_stats = self.directional_analysis['direction_stats']
        directions = list(direction_stats.keys())
        channel_counts = [stats['total_channels'] for stats in direction_stats.values()]

        axes[0,0].bar(directions, channel_counts, color=['red', 'green', 'blue'], alpha=0.7)
        axes[0,0].set_title('各方向通道数量分布')
        axes[0,0].set_ylabel('通道数量')

        # 测量类型分布
        measurement_types = ['acceleration', 'force', 'moment']
        x_pos = np.arange(len(directions))
        width = 0.25

        for i, mt in enumerate(measurement_types):
            counts = []
            for direction in directions:
                if mt in direction_stats[direction]['channels_by_type']:
                    counts.append(direction_stats[direction]['channels_by_type'][mt])
                else:
                    counts.append(0)

            axes[0,1].bar(x_pos + i*width, counts, width, label=mt, alpha=0.7)

        axes[0,1].set_title('各方向测量类型分布')
        axes[0,1].set_xlabel('方向')
        axes[0,1].set_ylabel('通道数量')
        axes[0,1].set_xticks(x_pos + width)
        axes[0,1].set_xticklabels(directions)
        axes[0,1].legend()

        # 跨位置相似性热图
        if 'cross_position_similarity' in self.directional_analysis:
            similarity_data = []
            similarity_labels = []

            for direction, sim_data in self.directional_analysis['cross_position_similarity'].items():
                for measurement_type, data in sim_data.items():
                    if 'avg_similarity' in data:
                        similarity_data.append(data['avg_similarity'])
                        similarity_labels.append(f"{direction}_{measurement_type}")

            if similarity_data:
                axes[1,0].bar(range(len(similarity_data)), similarity_data, alpha=0.7)
                axes[1,0].set_title('跨位置平均相似性')
                axes[1,0].set_ylabel('相似性系数')
                axes[1,0].set_xticks(range(len(similarity_labels)))
                axes[1,0].set_xticklabels(similarity_labels, rotation=45)

        # 聚类结果可视化
        if 'clustering_results' in self.directional_analysis:
            clustering = self.directional_analysis['clustering_results']
            pca_features = np.array(clustering['pca_features'])
            cluster_labels = clustering['cluster_labels']

            scatter = axes[1,1].scatter(pca_features[:, 0], pca_features[:, 1],
                                       c=cluster_labels, cmap='viridis', alpha=0.7, s=100)
            axes[1,1].set_title(f'方向性聚类结果 (轮廓系数: {clustering["silhouette_score"]:.3f})')
            axes[1,1].set_xlabel(f'PC1 ({clustering["pca_explained_variance"][0]:.2%})')
            axes[1,1].set_ylabel(f'PC2 ({clustering["pca_explained_variance"][1]:.2%})')

            # 添加标签
            for i, (channel, direction) in enumerate(zip(clustering['channel_labels'],
                                                        clustering['direction_labels'])):
                axes[1,1].annotate(f"{direction[0]}-{channel.split()[-1]}",
                                  (pca_features[i, 0], pca_features[i, 1]),
                                  xytext=(5, 5), textcoords='offset points', fontsize=8)

            plt.colorbar(scatter, ax=axes[1,1])

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'directional_force_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def generate_directional_insights(self):
        """生成方向性分析洞察"""
        print("生成方向性分析洞察...")

        insights = {
            'key_findings': [],
            'directional_recommendations': [],
            'physics_based_analysis': []
        }

        # 基于方向性的关键发现
        direction_stats = self.directional_analysis['direction_stats']

        insights['key_findings'].append("方向性通道分布分析:")
        for direction, stats in direction_stats.items():
            insights['key_findings'].append(
                f"  {direction}: {stats['total_channels']}个通道，包含{len(stats['measurement_types'])}种测量类型"
            )

        # 跨位置相似性分析
        if 'cross_position_similarity' in self.directional_analysis:
            insights['key_findings'].append("跨位置相似性发现:")

            for direction, sim_data in self.directional_analysis['cross_position_similarity'].items():
                for measurement_type, data in sim_data.items():
                    if 'avg_similarity' in data:
                        similarity = data['avg_similarity']
                        insights['key_findings'].append(
                            f"  {direction} {measurement_type}: 平均相似性 {similarity:.3f}"
                        )

        # 聚类分析结果
        if 'clustering_results' in self.directional_analysis:
            clustering = self.directional_analysis['clustering_results']
            silhouette = clustering['silhouette_score']

            insights['key_findings'].append(f"聚类分析结果: 轮廓系数 {silhouette:.3f}")

            # 分析聚类模式
            cluster_analysis = {}
            for i, (channel, direction, measurement, cluster) in enumerate(zip(
                clustering['channel_labels'],
                clustering['direction_labels'],
                clustering['measurement_labels'],
                clustering['cluster_labels']
            )):
                if cluster not in cluster_analysis:
                    cluster_analysis[cluster] = {'directions': set(), 'measurements': set(), 'channels': []}

                cluster_analysis[cluster]['directions'].add(direction)
                cluster_analysis[cluster]['measurements'].add(measurement)
                cluster_analysis[cluster]['channels'].append(channel)

            insights['key_findings'].append("聚类模式分析:")
            for cluster_id, analysis in cluster_analysis.items():
                insights['key_findings'].append(
                    f"  聚类{cluster_id}: {len(analysis['channels'])}个通道, "
                    f"涉及方向{list(analysis['directions'])}, "
                    f"测量类型{list(analysis['measurements'])}"
                )

        # 基于物理学的建议
        insights['physics_based_analysis'].extend([
            "碰撞力学分析表明，同方向的力传播具有相似的物理特性",
            "X方向（纵向）：主要承受正面碰撞的冲击力，传感器响应模式相似",
            "Y方向（横向）：主要承受侧面碰撞的冲击力，不同位置传感器具有协同响应",
            "Z方向（垂直）：主要承受翻滚和垂直冲击，传感器数据具有时序相关性"
        ])

        # 方向性建议
        insights['directional_recommendations'].extend([
            "优先开发X方向通用模型：正面碰撞最常见，数据量最大",
            "建立Y方向协同检测模型：侧面碰撞的多点协同响应特征明显",
            "开发Z方向时序关联模型：垂直方向的时序依赖性强",
            "考虑建立三维联合模型：综合XYZ三个方向的空间力学关系"
        ])

        self.directional_analysis['insights'] = insights
        return insights

    def save_results(self):
        """保存分析结果"""
        import json

        def convert_to_serializable(obj):
            """转换为JSON可序列化格式"""
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, set):
                return list(obj)
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif pd.isna(obj):
                return None
            else:
                return obj

        serializable_results = convert_to_serializable(self.directional_analysis)

        with open(os.path.join(self.output_dir, 'directional_force_analysis.json'), 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"方向性分析结果已保存到 {self.output_dir}")

def main():
    """主函数"""
    data_file = "results/multi_channel_analysis/multi_channel_data.json"
    channel_metadata_file = "results/multi_channel_analysis/channel_metadata.json"
    output_dir = "results/directional_force_analysis"

    # 创建分析器
    analyzer = DirectionalForceAnalyzer(data_file, channel_metadata_file, output_dir)

    # 执行分析
    analyzer.load_data()
    analyzer.extract_directional_channels()
    analyzer.analyze_cross_position_similarity()
    analyzer.analyze_directional_clustering()
    analyzer.create_directional_visualizations()
    insights = analyzer.generate_directional_insights()
    analyzer.save_results()

    # 打印关键洞察
    print("\n" + "="*60)
    print("力方向性聚类分析结果")
    print("="*60)

    print("\n关键发现:")
    for finding in insights['key_findings']:
        print(f"• {finding}")

    print("\n物理学分析:")
    for analysis in insights['physics_based_analysis']:
        print(f"• {analysis}")

    print("\n方向性建议:")
    for rec in insights['directional_recommendations']:
        print(f"• {rec}")

    print(f"\n分析完成，详细结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
