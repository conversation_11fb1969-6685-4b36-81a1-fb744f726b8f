# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多通道数据分析脚本
用于分析碰撞测试数据中的多个传感器通道特征，
评估通用模型训练的可行性，分析不同通道间的数据特征和相关性，
为多通道异常检测模型的设计和训练策略提供数据支持。
"""

import os
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import json
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class MultiChannelAnalyzer:
    """多通道数据分析器"""

    def __init__(self, raw_data_path):
        self.raw_data_path = raw_data_path
        self.channel_metadata = {}
        self.channel_statistics = {}
        self.dataset_info = {}

    def scan_datasets(self):
        """扫描所有数据集，统计通道信息"""
        print("开始扫描数据集...")

        dataset_dirs = [d for d in os.listdir(self.raw_data_path)
                       if os.path.isdir(os.path.join(self.raw_data_path, d))]

        for dataset_dir in dataset_dirs:
            dataset_path = os.path.join(self.raw_data_path, dataset_dir)
            channel_dir = None

            # 查找CHANNEL目录
            for item in os.listdir(dataset_path):
                item_path = os.path.join(dataset_path, item)
                if os.path.isdir(item_path):
                    channel_subdir = os.path.join(item_path, 'CHANNEL')
                    if os.path.exists(channel_subdir):
                        channel_dir = channel_subdir
                        break

            if channel_dir:
                self._analyze_dataset_channels(dataset_dir, channel_dir)
            else:
                print(f"警告: 未找到 {dataset_dir} 的CHANNEL目录")

        print(f"扫描完成，共分析了 {len(self.dataset_info)} 个数据集")

    def _analyze_dataset_channels(self, dataset_name, channel_dir):
        """分析单个数据集的通道信息"""
        # 查找.chn文件
        chn_files = glob.glob(os.path.join(channel_dir, "*.chn"))

        if not chn_files:
            print(f"警告: {dataset_name} 中未找到.chn文件")
            return

        chn_file = chn_files[0]  # 取第一个.chn文件

        try:
            channels = self._parse_chn_file(chn_file)
            self.dataset_info[dataset_name] = {
                'channel_count': len(channels),
                'channels': channels,
                'chn_file': chn_file
            }

            # 统计通道元数据
            for channel in channels:
                channel_name = channel['name']
                if channel_name not in self.channel_metadata:
                    self.channel_metadata[channel_name] = {
                        'datasets': [],
                        'channel_codes': set(),
                        'units': set()
                    }

                self.channel_metadata[channel_name]['datasets'].append(dataset_name)
                self.channel_metadata[channel_name]['channel_codes'].add(channel['code'])

        except Exception as e:
            print(f"解析 {dataset_name} 的.chn文件时出错: {str(e)}")

    def _parse_chn_file(self, chn_file):
        """解析.chn文件，提取通道信息"""
        channels = []

        with open(chn_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        for line in lines:
            line = line.strip()
            if line.startswith('Name of channel'):
                # 解析格式: Name of channel 001 :11HEAD0000H3ACXP / Head Acceleration X
                parts = line.split(':', 1)
                if len(parts) == 2:
                    channel_info = parts[1].strip()
                    if ' / ' in channel_info:
                        code, name = channel_info.split(' / ', 1)
                        channels.append({
                            'code': code.strip(),
                            'name': name.strip()
                        })

        return channels

    def generate_channel_statistics(self):
        """生成通道统计信息"""
        print("生成通道统计信息...")

        # 统计通道出现频率
        channel_frequency = {}
        for channel_name, info in self.channel_metadata.items():
            channel_frequency[channel_name] = len(info['datasets'])

        # 统计数据集通道数量分布
        channel_count_distribution = Counter([info['channel_count']
                                            for info in self.dataset_info.values()])

        self.channel_statistics = {
            'total_datasets': len(self.dataset_info),
            'unique_channels': len(self.channel_metadata),
            'channel_frequency': channel_frequency,
            'channel_count_distribution': dict(channel_count_distribution),
            'most_common_channels': sorted(channel_frequency.items(),
                                         key=lambda x: x[1], reverse=True)[:10]
        }

        return self.channel_statistics

    def identify_channel_groups(self):
        """识别通道分组（按身体部位和测量类型）"""
        print("识别通道分组...")

        channel_groups = {
            'Head': [],
            'Neck': [],
            'Chest': [],
            'Pelvis': [],
            'Femur': [],
            'Tibia': [],
            'Other': []
        }

        measurement_types = {
            'Acceleration': [],
            'Force': [],
            'Moment': [],
            'Displacement': [],
            'Velocity': [],
            'Other': []
        }

        for channel_name in self.channel_metadata.keys():
            # 按身体部位分组
            if 'Head' in channel_name:
                channel_groups['Head'].append(channel_name)
            elif 'Neck' in channel_name:
                channel_groups['Neck'].append(channel_name)
            elif 'Chest' in channel_name:
                channel_groups['Chest'].append(channel_name)
            elif 'Pelvis' in channel_name:
                channel_groups['Pelvis'].append(channel_name)
            elif 'Femur' in channel_name:
                channel_groups['Femur'].append(channel_name)
            elif 'Tibia' in channel_name:
                channel_groups['Tibia'].append(channel_name)
            else:
                channel_groups['Other'].append(channel_name)

            # 按测量类型分组
            if 'Acceleration' in channel_name:
                measurement_types['Acceleration'].append(channel_name)
            elif 'Force' in channel_name:
                measurement_types['Force'].append(channel_name)
            elif 'Moment' in channel_name:
                measurement_types['Moment'].append(channel_name)
            elif 'Displacement' in channel_name:
                measurement_types['Displacement'].append(channel_name)
            elif 'Velocity' in channel_name:
                measurement_types['Velocity'].append(channel_name)
            else:
                measurement_types['Other'].append(channel_name)

        return channel_groups, measurement_types

    def save_analysis_results(self, output_dir):
        """保存分析结果"""
        os.makedirs(output_dir, exist_ok=True)

        # 保存数据集信息
        with open(os.path.join(output_dir, 'dataset_info.json'), 'w', encoding='utf-8') as f:
            # 转换set为list以便JSON序列化
            dataset_info_serializable = {}
            for dataset, info in self.dataset_info.items():
                dataset_info_serializable[dataset] = {
                    'channel_count': info['channel_count'],
                    'channels': info['channels'],
                    'chn_file': info['chn_file']
                }
            json.dump(dataset_info_serializable, f, ensure_ascii=False, indent=2)

        # 保存通道元数据
        with open(os.path.join(output_dir, 'channel_metadata.json'), 'w', encoding='utf-8') as f:
            channel_metadata_serializable = {}
            for channel, info in self.channel_metadata.items():
                channel_metadata_serializable[channel] = {
                    'datasets': info['datasets'],
                    'channel_codes': list(info['channel_codes']),
                    'units': list(info['units'])
                }
            json.dump(channel_metadata_serializable, f, ensure_ascii=False, indent=2)

        # 保存统计信息
        with open(os.path.join(output_dir, 'channel_statistics.json'), 'w', encoding='utf-8') as f:
            json.dump(self.channel_statistics, f, ensure_ascii=False, indent=2)

        print(f"分析结果已保存到 {output_dir}")

    def extract_multi_channel_data(self, target_channels, max_datasets=10):
        """提取多个通道的时序数据用于分析"""
        print(f"开始提取多通道数据: {target_channels}")

        multi_channel_data = {}
        dataset_count = 0

        for dataset_name, dataset_info in self.dataset_info.items():
            if dataset_count >= max_datasets:
                break

            # 检查数据集是否包含目标通道
            dataset_channels = [ch['name'] for ch in dataset_info['channels']]
            if not all(ch in dataset_channels for ch in target_channels):
                continue

            # 查找数据集的CHANNEL目录
            dataset_path = os.path.join(self.raw_data_path, dataset_name)
            channel_dir = None

            for item in os.listdir(dataset_path):
                item_path = os.path.join(dataset_path, item)
                if os.path.isdir(item_path):
                    channel_subdir = os.path.join(item_path, 'CHANNEL')
                    if os.path.exists(channel_subdir):
                        channel_dir = channel_subdir
                        break

            if not channel_dir:
                continue

            # 提取每个目标通道的数据
            dataset_data = {}
            for target_channel in target_channels:
                # 找到对应的通道编号
                channel_code = None
                for ch in dataset_info['channels']:
                    if ch['name'] == target_channel:
                        channel_code = ch['code']
                        break

                if not channel_code:
                    continue

                # 查找对应的数据文件
                channel_files = glob.glob(os.path.join(channel_dir, "*.0*"))
                for channel_file in channel_files:
                    try:
                        metadata, time_series_data = self._parse_channel_data_file(channel_file)
                        if metadata.get('Channel code', '') == channel_code:
                            dataset_data[target_channel] = {
                                'data': time_series_data,
                                'metadata': metadata,
                                'file_path': channel_file
                            }
                            break
                    except Exception as e:
                        continue

            if len(dataset_data) == len(target_channels):
                multi_channel_data[dataset_name] = dataset_data
                dataset_count += 1
                print(f"成功提取 {dataset_name} 的多通道数据")

        print(f"共提取了 {len(multi_channel_data)} 个数据集的多通道数据")
        return multi_channel_data

    def _parse_channel_data_file(self, file_path):
        """解析通道数据文件，提取元数据和时间序列数据"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        # 提取元数据
        metadata = {}
        data_start_idx = 0
        for i, line in enumerate(lines):
            if ':' in line:
                key, value = line.strip().split(':', 1)
                metadata[key.strip()] = value.strip()
            else:
                # 找到数据开始的行
                data_start_idx = i
                break

        # 提取时间序列数据
        time_series_data = []
        for line in lines[data_start_idx:]:
            try:
                value = float(line.strip())
                time_series_data.append(value)
            except ValueError:
                # 跳过无法转换为浮点数的行
                continue

        return metadata, np.array(time_series_data)

def main():
    """主函数"""
    # 设置路径
    raw_data_path = "data/raw"
    output_dir = "results/multi_channel_analysis"

    # 创建分析器
    analyzer = MultiChannelAnalyzer(raw_data_path)

    # 执行分析
    analyzer.scan_datasets()
    analyzer.generate_channel_statistics()
    channel_groups, measurement_types = analyzer.identify_channel_groups()

    # 打印基本统计信息
    print("\n=== 数据集统计信息 ===")
    print(f"总数据集数量: {analyzer.channel_statistics['total_datasets']}")
    print(f"唯一通道数量: {analyzer.channel_statistics['unique_channels']}")
    print(f"通道数量分布: {analyzer.channel_statistics['channel_count_distribution']}")

    print("\n=== 最常见的通道 ===")
    for channel, count in analyzer.channel_statistics['most_common_channels']:
        print(f"{channel}: {count} 个数据集")

    print("\n=== 按身体部位分组 ===")
    for body_part, channels in channel_groups.items():
        if channels:
            print(f"{body_part}: {len(channels)} 个通道")

    print("\n=== 按测量类型分组 ===")
    for measurement_type, channels in measurement_types.items():
        if channels:
            print(f"{measurement_type}: {len(channels)} 个通道")

    # 保存结果
    analyzer.save_analysis_results(output_dir)

    # 提取核心通道数据进行深入分析
    core_channels = [
        'Head Acceleration X',
        'Head Acceleration Y',
        'Head Acceleration Z',
        'Chest Acceleration X',
        'Chest Acceleration Y',
        'Chest Acceleration Z'
    ]

    print(f"\n=== 提取核心通道数据进行分析 ===")
    multi_channel_data = analyzer.extract_multi_channel_data(core_channels, max_datasets=20)

    # 保存多通道数据
    if multi_channel_data:
        multi_channel_file = os.path.join(output_dir, 'multi_channel_data.json')
        # 转换numpy数组为列表以便JSON序列化
        serializable_data = {}
        for dataset, channels in multi_channel_data.items():
            serializable_data[dataset] = {}
            for channel, info in channels.items():
                serializable_data[dataset][channel] = {
                    'data': info['data'].tolist(),
                    'metadata': info['metadata'],
                    'file_path': info['file_path']
                }

        with open(multi_channel_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)

        print(f"多通道数据已保存到 {multi_channel_file}")

if __name__ == "__main__":
    main()
