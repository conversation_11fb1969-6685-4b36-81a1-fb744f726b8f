# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细ROI投资回报率分析脚本
基于实际业务场景和成本结构进行精确的投资回报率分析，
包含年化收益计算、开发成本分析、ROI指标计算和可视化。
为项目投资决策提供详细的财务分析支持。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class DetailedROIAnalyzer:
    """详细ROI分析器"""

    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.roi_analysis = {}

        # 业务参数
        self.annual_tests = 2000  # 年测试次数
        self.time_saved_per_test_minutes = 50  # 每次测试节省时间（分钟）
        self.engineer_hourly_rate = 200  # 工程师时薪（元）
        self.working_days_per_month = 21.75  # 月工作日
        self.working_hours_per_day = 8  # 日工作小时

        # 人员成本（元/人月）
        self.ai_engineer_monthly_cost = 30000  # AI工程师
        self.software_engineer_monthly_cost = 20000  # 软件工程师
        self.mechanics_engineer_monthly_cost = 20000  # 力学工程师

        # 项目约束
        self.total_budget_limit = 1200000  # 总预算限制120万元

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def calculate_annual_benefits(self):
        """计算年化收益"""
        print("计算年化收益...")

        # 时间节省收益
        time_saved_hours_per_year = (self.annual_tests * self.time_saved_per_test_minutes) / 60
        time_saving_benefit = time_saved_hours_per_year * self.engineer_hourly_rate

        # 模型维护成本节省（从1298个模型减少到10个模型）
        original_models = 1298
        optimized_models = 10
        model_reduction_ratio = (original_models - optimized_models) / original_models

        # 假设每个模型年维护成本2000元（包括监控、更新、故障处理）
        model_maintenance_cost_per_year = 2000
        maintenance_saving = original_models * model_maintenance_cost_per_year * model_reduction_ratio

        # 新项目部署效率提升收益
        # 假设每年新增5个项目，每个项目部署时间从4周缩短到1天
        new_projects_per_year = 5
        deployment_time_saved_weeks = 4 - 0.2  # 从4周缩短到1天(0.2周)
        deployment_cost_per_week = self.software_engineer_monthly_cost / 4  # 周成本
        deployment_saving = new_projects_per_year * deployment_time_saved_weeks * deployment_cost_per_week

        # 质量提升带来的风险降低收益
        # 假设通用模型降低10%的误报率，每次误报处理成本500元
        false_positive_reduction = 0.1
        false_positive_cost = 500
        original_false_positive_rate = 0.05  # 原始误报率5%
        quality_improvement_saving = (self.annual_tests * original_false_positive_rate *
                                    false_positive_reduction * false_positive_cost)

        annual_benefits = {
            'time_saving_benefit': time_saving_benefit,
            'maintenance_saving': maintenance_saving,
            'deployment_saving': deployment_saving,
            'quality_improvement_saving': quality_improvement_saving,
            'total_annual_benefit': (time_saving_benefit + maintenance_saving +
                                   deployment_saving + quality_improvement_saving)
        }

        self.roi_analysis['annual_benefits'] = annual_benefits
        return annual_benefits

    def calculate_development_costs(self):
        """计算开发成本结构"""
        print("计算开发成本结构...")

        # 人员配置和工期（120万预算版，增强人员配置）
        team_config = {
            'ai_engineers': {
                'count': 2,
                'months': 6,
                'monthly_cost': self.ai_engineer_monthly_cost,
                'description': '负责模型设计、训练和优化'
            },
            'software_engineers': {
                'count': 2,
                'months': 6.5,
                'monthly_cost': self.software_engineer_monthly_cost,
                'description': '负责系统集成、接口开发和部署'
            },
            'mechanics_engineers': {
                'count': 1,
                'months': 4,
                'monthly_cost': self.mechanics_engineer_monthly_cost,
                'description': '负责力学理论验证和物理约束设计'
            },
            'project_manager': {
                'count': 1,  # 全职项目管理
                'months': 6.5,
                'monthly_cost': 22000,
                'description': '项目管理和协调（全职）'
            },
            'qa_engineer': {
                'count': 1,
                'months': 4,
                'monthly_cost': 18000,
                'description': '质量保证和测试工程师'
            }
        }

        # 计算人员成本
        personnel_costs = {}
        total_personnel_cost = 0

        for role, config in team_config.items():
            cost = config['count'] * config['months'] * config['monthly_cost']
            personnel_costs[role] = {
                'cost': cost,
                'details': f"{config['count']}人 × {config['months']}月 × {config['monthly_cost']:,}元/月",
                'description': config['description']
            }
            total_personnel_cost += cost

        # 硬件和基础设施成本（120万预算版，适度提升）
        hardware_costs = {
            'gpu_cloud_rental': {
                'cost': 105000,
                'description': '云GPU租用（6×RTX 4090等效）',
                'details': '租用6个月，月租17,500元，支持并行训练'
            },
            'development_workstations': {
                'cost': 60000,
                'description': '3台高性能开发工作站',
                'details': '每台20,000元，团队开发使用'
            },
            'cloud_services': {
                'cost': 25000,
                'description': '云服务和存储',
                'details': '数据存储、备份、测试环境和CI/CD'
            },
            'software_licenses': {
                'cost': 15000,
                'description': '软件许可证和工具',
                'details': '开发工具、监控软件、数据库等'
            }
        }

        total_hardware_cost = sum(item['cost'] for item in hardware_costs.values())

        # 其他成本（120万预算版，适度提升）
        other_costs = {
            'training_and_certification': {
                'cost': 12000,
                'description': '团队培训和技术认证'
            },
            'testing_and_validation': {
                'cost': 25000,
                'description': '测试数据采集和验证'
            },
            'documentation_and_delivery': {
                'cost': 8000,
                'description': '文档编写和项目交付'
            },
            'external_consulting': {
                'cost': 10000,
                'description': '外部技术咨询和评审'
            },
            'contingency': {
                'cost': 25000,
                'description': '风险预留（约2%）'
            }
        }

        total_other_cost = sum(item['cost'] for item in other_costs.values())

        # 总成本计算
        total_development_cost = total_personnel_cost + total_hardware_cost + total_other_cost

        development_costs = {
            'personnel_costs': personnel_costs,
            'hardware_costs': hardware_costs,
            'other_costs': other_costs,
            'total_personnel_cost': total_personnel_cost,
            'total_hardware_cost': total_hardware_cost,
            'total_other_cost': total_other_cost,
            'total_development_cost': total_development_cost,
            'budget_utilization': total_development_cost / self.total_budget_limit
        }

        self.roi_analysis['development_costs'] = development_costs
        return development_costs

    def calculate_roi_metrics(self):
        """计算ROI指标"""
        print("计算ROI指标...")

        annual_benefits = self.roi_analysis['annual_benefits']
        development_costs = self.roi_analysis['development_costs']

        total_investment = development_costs['total_development_cost']
        annual_return = annual_benefits['total_annual_benefit']

        # ROI计算
        roi_metrics = {
            'total_investment': total_investment,
            'annual_return': annual_return,
            'roi_percentage': (annual_return - total_investment) / total_investment * 100,
            'payback_period_months': total_investment / (annual_return / 12),
            'npv_3_years': self._calculate_npv(total_investment, annual_return, 3, 0.08),
            'irr': self._calculate_irr(total_investment, annual_return, 3)
        }

        self.roi_analysis['roi_metrics'] = roi_metrics
        return roi_metrics

    def _calculate_npv(self, investment, annual_return, years, discount_rate):
        """计算净现值"""
        npv = -investment
        for year in range(1, years + 1):
            npv += annual_return / ((1 + discount_rate) ** year)
        return npv

    def _calculate_irr(self, investment, annual_return, years):
        """计算内部收益率（简化计算）"""
        # 简化IRR计算：假设等额年金
        if annual_return <= 0:
            return 0

        # 使用二分法求解IRR
        low, high = 0, 1
        for _ in range(100):  # 迭代100次
            mid = (low + high) / 2
            npv = -investment + sum(annual_return / ((1 + mid) ** year) for year in range(1, years + 1))
            if abs(npv) < 1000:  # 精度1000元
                return mid * 100
            elif npv > 0:
                low = mid
            else:
                high = mid
        return mid * 100

    def create_roi_visualizations(self):
        """创建ROI可视化"""
        print("生成ROI可视化...")

        # 1. 成本结构饼图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('详细ROI分析', fontsize=16, fontweight='bold')

        development_costs = self.roi_analysis['development_costs']

        # 成本结构
        cost_categories = ['人员成本', '硬件成本', '其他成本']
        cost_values = [
            development_costs['total_personnel_cost'],
            development_costs['total_hardware_cost'],
            development_costs['total_other_cost']
        ]

        axes[0,0].pie(cost_values, labels=cost_categories, autopct='%1.1f%%', startangle=90)
        axes[0,0].set_title('开发成本结构')

        # 收益结构
        annual_benefits = self.roi_analysis['annual_benefits']
        benefit_categories = ['时间节省', '维护节省', '部署节省', '质量提升']
        benefit_values = [
            annual_benefits['time_saving_benefit'],
            annual_benefits['maintenance_saving'],
            annual_benefits['deployment_saving'],
            annual_benefits['quality_improvement_saving']
        ]

        axes[0,1].pie(benefit_values, labels=benefit_categories, autopct='%1.1f%%', startangle=90)
        axes[0,1].set_title('年化收益结构')

        # 投资回收期
        roi_metrics = self.roi_analysis['roi_metrics']
        months = range(1, 37)  # 3年
        cumulative_return = []
        investment = roi_metrics['total_investment']
        monthly_return = roi_metrics['annual_return'] / 12

        for month in months:
            cumulative = month * monthly_return - investment
            cumulative_return.append(cumulative)

        axes[1,0].plot(months, cumulative_return, linewidth=2, color='green')
        axes[1,0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1,0].axvline(x=roi_metrics['payback_period_months'], color='blue', linestyle='--', alpha=0.7)
        axes[1,0].set_xlabel('月份')
        axes[1,0].set_ylabel('累积净收益（元）')
        axes[1,0].set_title(f'投资回收期：{roi_metrics["payback_period_months"]:.1f}个月')
        axes[1,0].grid(True, alpha=0.3)

        # ROI对比
        scenarios = ['当前方案', '通用模型方案']
        roi_values = [0, roi_metrics['roi_percentage']]
        colors = ['red', 'green']

        bars = axes[1,1].bar(scenarios, roi_values, color=colors, alpha=0.7)
        axes[1,1].set_ylabel('ROI (%)')
        axes[1,1].set_title('ROI对比')

        # 添加数值标签
        for bar, value in zip(bars, roi_values):
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + 5,
                          f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'detailed_roi_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def generate_detailed_report(self):
        """生成详细报告"""
        print("生成详细ROI报告...")

        annual_benefits = self.roi_analysis['annual_benefits']
        development_costs = self.roi_analysis['development_costs']
        roi_metrics = self.roi_analysis['roi_metrics']

        report = {
            'executive_summary': {
                'total_investment': f"{development_costs['total_development_cost']:,.0f}元",
                'annual_return': f"{annual_benefits['total_annual_benefit']:,.0f}元",
                'roi_percentage': f"{roi_metrics['roi_percentage']:.1f}%",
                'payback_period': f"{roi_metrics['payback_period_months']:.1f}个月",
                'budget_compliance': f"预算利用率{development_costs['budget_utilization']*100:.1f}%"
            },
            'detailed_calculations': {
                'time_saving_calculation': (
                    f"年测试次数：{self.annual_tests:,}次\n"
                    f"每次节省时间：{self.time_saved_per_test_minutes}分钟\n"
                    f"工程师时薪：{self.engineer_hourly_rate}元/小时\n"
                    f"年节省工时：{(self.annual_tests * self.time_saved_per_test_minutes) / 60:,.0f}小时\n"
                    f"时间节省收益：{annual_benefits['time_saving_benefit']:,.0f}元/年"
                ),
                'maintenance_saving_calculation': (
                    f"原模型数量：1,298个\n"
                    f"优化后模型数量：10个\n"
                    f"模型减少比例：{((1298-10)/1298)*100:.1f}%\n"
                    f"单模型年维护成本：2,000元\n"
                    f"维护成本节省：{annual_benefits['maintenance_saving']:,.0f}元/年"
                ),
                'team_cost_calculation': (
                    f"AI工程师：2人 × 6月 × {self.ai_engineer_monthly_cost:,}元/月 = {2*6*self.ai_engineer_monthly_cost:,}元\n"
                    f"软件工程师：2人 × 8月 × {self.software_engineer_monthly_cost:,}元/月 = {2*8*self.software_engineer_monthly_cost:,}元\n"
                    f"力学工程师：1人 × 4月 × {self.mechanics_engineer_monthly_cost:,}元/月 = {1*4*self.mechanics_engineer_monthly_cost:,}元\n"
                    f"项目经理：1人 × 8月 × 25,000元/月 = {1*8*25000:,}元\n"
                    f"人员成本小计：{development_costs['total_personnel_cost']:,}元"
                )
            }
        }

        self.roi_analysis['detailed_report'] = report
        return report

    def save_results(self):
        """保存分析结果"""
        import json

        def convert_to_serializable(obj):
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif pd.isna(obj):
                return None
            else:
                return obj

        serializable_results = convert_to_serializable(self.roi_analysis)

        with open(os.path.join(self.output_dir, 'detailed_roi_analysis.json'), 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"详细ROI分析结果已保存到 {self.output_dir}")

def main():
    """主函数"""
    output_dir = "results/detailed_roi_analysis"

    # 创建分析器
    analyzer = DetailedROIAnalyzer(output_dir)

    # 执行分析
    annual_benefits = analyzer.calculate_annual_benefits()
    development_costs = analyzer.calculate_development_costs()
    roi_metrics = analyzer.calculate_roi_metrics()
    analyzer.create_roi_visualizations()
    detailed_report = analyzer.generate_detailed_report()
    analyzer.save_results()

    # 打印详细报告
    print("\n" + "="*80)
    print("详细ROI分析报告")
    print("="*80)

    print(f"\n📊 执行摘要:")
    for key, value in detailed_report['executive_summary'].items():
        print(f"• {key}: {value}")

    print(f"\n💰 年化收益明细:")
    print(f"• 时间节省收益: {annual_benefits['time_saving_benefit']:,.0f}元")
    print(f"• 维护成本节省: {annual_benefits['maintenance_saving']:,.0f}元")
    print(f"• 部署效率提升: {annual_benefits['deployment_saving']:,.0f}元")
    print(f"• 质量改进收益: {annual_benefits['quality_improvement_saving']:,.0f}元")
    print(f"• 年化收益总计: {annual_benefits['total_annual_benefit']:,.0f}元")

    print(f"\n💸 开发成本明细:")
    print(f"• 人员成本: {development_costs['total_personnel_cost']:,.0f}元")
    print(f"• 硬件成本: {development_costs['total_hardware_cost']:,.0f}元")
    print(f"• 其他成本: {development_costs['total_other_cost']:,.0f}元")
    print(f"• 开发成本总计: {development_costs['total_development_cost']:,.0f}元")
    print(f"• 预算利用率: {development_costs['budget_utilization']*100:.1f}%")

    print(f"\n📈 ROI指标:")
    print(f"• 投资回报率: {roi_metrics['roi_percentage']:.1f}%")
    print(f"• 投资回收期: {roi_metrics['payback_period_months']:.1f}个月")
    print(f"• 3年净现值: {roi_metrics['npv_3_years']:,.0f}元")
    print(f"• 内部收益率: {roi_metrics['irr']:.1f}%")

    print(f"\n分析完成，详细结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
