# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型训练时间成本分析脚本
基于实际训练基准计算不同聚类方案的时间成本，
分析单通道模型、位置聚类、方向性聚类等不同策略的训练时间和资源需求，
为模型训练策略选择提供量化的成本分析支持。
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class TrainingCostAnalyzer:
    """训练成本分析器"""

    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.cost_analysis = {}

        # 基准参数
        self.base_datasets = 200  # 基准数据集数量
        self.base_epochs = 100    # 基准训练轮数
        self.base_time_hours = 2  # 基准训练时间（小时）
        self.target_epochs = 300  # 目标训练轮数

        # 项目参数
        self.total_channels = 1298  # 客户总通道需求
        self.available_datasets = 243  # 可用数据集数量

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)

    def calculate_base_metrics(self):
        """计算基础训练指标"""
        # 计算单位时间成本
        time_per_dataset_per_epoch = self.base_time_hours / (self.base_datasets * self.base_epochs)
        time_per_model_base = self.available_datasets * self.target_epochs * time_per_dataset_per_epoch

        self.cost_analysis['base_metrics'] = {
            'time_per_dataset_per_epoch_hours': time_per_dataset_per_epoch,
            'time_per_model_target_hours': time_per_model_base,
            'time_per_model_target_days': time_per_model_base / 24,
            'base_datasets': self.base_datasets,
            'base_epochs': self.base_epochs,
            'base_time_hours': self.base_time_hours,
            'target_epochs': self.target_epochs,
            'available_datasets': self.available_datasets
        }

        return time_per_model_base

    def analyze_clustering_scenarios(self):
        """分析不同聚类方案的成本"""
        base_time_per_model = self.calculate_base_metrics()

        scenarios = {
            '单通道模型方案': {
                'description': '每个通道独立训练一个模型',
                'model_count': self.total_channels,
                'data_per_model': self.available_datasets,
                'efficiency_factor': 1.0,  # 基准效率
                'parallel_factor': 1.0     # 无并行优势
            },

            '位置聚类方案': {
                'description': 'POS1/POS3/POS4位置分组，每个位置一个通用模型',
                'model_count': 4,  # POS1, POS3, POS4, 其他
                'data_per_model': self.available_datasets * 200,  # 平均每个位置200+通道
                'efficiency_factor': 0.8,  # 通用模型训练稍复杂
                'parallel_factor': 4.0     # 可以并行训练4个模型
            },

            '方向性聚类方案': {
                'description': 'X/Y/Z方向分组，每个方向一个通用模型',
                'model_count': 3,  # X, Y, Z方向
                'data_per_model': self.available_datasets * 400,  # 平均每个方向400+通道
                'efficiency_factor': 0.7,  # 方向性模型更复杂但更有效
                'parallel_factor': 3.0     # 可以并行训练3个模型
            },

            '混合聚类方案': {
                'description': '位置+方向性+测量类型综合聚类',
                'model_count': 10,  # 综合考虑各种因素
                'data_per_model': self.available_datasets * 100,  # 平均每个模型100+通道
                'efficiency_factor': 0.9,  # 平衡复杂度和效率
                'parallel_factor': 10.0    # 可以并行训练10个模型
            },

            '超级通用模型': {
                'description': '单一超级模型处理所有通道',
                'model_count': 1,
                'data_per_model': self.available_datasets * self.total_channels,
                'efficiency_factor': 0.5,  # 超级复杂但高效
                'parallel_factor': 1.0     # 只有一个模型
            }
        }

        # 计算每个方案的成本
        for scenario_name, params in scenarios.items():
            # 单个模型训练时间（考虑数据量和复杂度）
            data_scale_factor = params['data_per_model'] / self.available_datasets
            single_model_time = base_time_per_model * data_scale_factor * params['efficiency_factor']

            # 总训练时间（考虑并行）
            total_sequential_time = single_model_time * params['model_count']
            total_parallel_time = total_sequential_time / params['parallel_factor']

            # 成本分析
            scenarios[scenario_name].update({
                'single_model_time_hours': single_model_time,
                'single_model_time_days': single_model_time / 24,
                'total_sequential_time_hours': total_sequential_time,
                'total_sequential_time_days': total_sequential_time / 24,
                'total_parallel_time_hours': total_parallel_time,
                'total_parallel_time_days': total_parallel_time / 24,
                'time_saving_vs_single_channel': 1 - (total_parallel_time / (base_time_per_model * self.total_channels)),
                'cost_efficiency_score': (self.total_channels / params['model_count']) / (total_parallel_time / base_time_per_model)
            })

        self.cost_analysis['scenarios'] = scenarios
        return scenarios

    def create_cost_visualizations(self):
        """创建成本分析可视化"""
        scenarios = self.cost_analysis['scenarios']

        # 准备数据
        scenario_names = list(scenarios.keys())
        model_counts = [scenarios[name]['model_count'] for name in scenario_names]
        parallel_times_days = [scenarios[name]['total_parallel_time_days'] for name in scenario_names]
        time_savings = [scenarios[name]['time_saving_vs_single_channel'] * 100 for name in scenario_names]
        efficiency_scores = [scenarios[name]['cost_efficiency_score'] for name in scenario_names]

        # 创建综合对比图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('模型训练时间成本分析', fontsize=16, fontweight='bold')

        # 1. 模型数量对比
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
        bars1 = axes[0,0].bar(range(len(scenario_names)), model_counts, color=colors, alpha=0.8)
        axes[0,0].set_title('不同方案的模型数量')
        axes[0,0].set_ylabel('模型数量')
        axes[0,0].set_xticks(range(len(scenario_names)))
        axes[0,0].set_xticklabels([name.replace('方案', '') for name in scenario_names], rotation=45)
        axes[0,0].set_yscale('log')  # 使用对数刻度

        # 添加数值标签
        for bar, count in zip(bars1, model_counts):
            height = bar.get_height()
            axes[0,0].text(bar.get_x() + bar.get_width()/2., height,
                          f'{count}', ha='center', va='bottom')

        # 2. 训练时间对比（并行）
        bars2 = axes[0,1].bar(range(len(scenario_names)), parallel_times_days, color=colors, alpha=0.8)
        axes[0,1].set_title('训练时间对比（并行执行）')
        axes[0,1].set_ylabel('训练时间（天）')
        axes[0,1].set_xticks(range(len(scenario_names)))
        axes[0,1].set_xticklabels([name.replace('方案', '') for name in scenario_names], rotation=45)

        # 添加数值标签
        for bar, time_days in zip(bars2, parallel_times_days):
            height = bar.get_height()
            axes[0,1].text(bar.get_x() + bar.get_width()/2., height,
                          f'{time_days:.1f}天', ha='center', va='bottom')

        # 3. 时间节省百分比
        bars3 = axes[1,0].bar(range(len(scenario_names)), time_savings, color=colors, alpha=0.8)
        axes[1,0].set_title('相对单通道方案的时间节省')
        axes[1,0].set_ylabel('时间节省（%）')
        axes[1,0].set_xticks(range(len(scenario_names)))
        axes[1,0].set_xticklabels([name.replace('方案', '') for name in scenario_names], rotation=45)

        # 添加数值标签
        for bar, saving in zip(bars3, time_savings):
            height = bar.get_height()
            axes[1,0].text(bar.get_x() + bar.get_width()/2., height,
                          f'{saving:.1f}%', ha='center', va='bottom')

        # 4. 成本效率评分
        bars4 = axes[1,1].bar(range(len(scenario_names)), efficiency_scores, color=colors, alpha=0.8)
        axes[1,1].set_title('成本效率评分')
        axes[1,1].set_ylabel('效率评分')
        axes[1,1].set_xticks(range(len(scenario_names)))
        axes[1,1].set_xticklabels([name.replace('方案', '') for name in scenario_names], rotation=45)

        # 添加数值标签
        for bar, score in zip(bars4, efficiency_scores):
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height,
                          f'{score:.1f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'training_cost_analysis.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 创建详细时间线图
        self._create_timeline_chart()

    def _create_timeline_chart(self):
        """创建训练时间线图"""
        scenarios = self.cost_analysis['scenarios']

        fig, ax = plt.subplots(figsize=(14, 8))

        scenario_names = list(scenarios.keys())
        y_positions = range(len(scenario_names))

        # 绘制时间线
        for i, (name, data) in enumerate(scenarios.items()):
            # 串行时间（浅色）
            ax.barh(i, data['total_sequential_time_days'],
                   color='lightcoral', alpha=0.6, label='串行时间' if i == 0 else "")

            # 并行时间（深色）
            ax.barh(i, data['total_parallel_time_days'],
                   color='darkred', alpha=0.8, label='并行时间' if i == 0 else "")

            # 添加时间标签
            ax.text(data['total_parallel_time_days'] + 1, i,
                   f'{data["total_parallel_time_days"]:.1f}天',
                   va='center', fontweight='bold')

        ax.set_yticks(y_positions)
        ax.set_yticklabels([name.replace('方案', '') for name in scenario_names])
        ax.set_xlabel('训练时间（天）')
        ax.set_title('不同聚类方案训练时间对比', fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'images', 'training_timeline.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def generate_cost_insights(self):
        """生成成本分析洞察"""
        scenarios = self.cost_analysis['scenarios']
        base_metrics = self.cost_analysis['base_metrics']

        insights = {
            'training_baseline': [],
            'cost_comparison': [],
            'recommendations': [],
            'risk_analysis': []
        }

        # 训练基准分析
        insights['training_baseline'].extend([
            f"基准训练配置：{self.base_datasets}个数据集，{self.base_epochs}轮训练，耗时{self.base_time_hours}小时",
            f"目标训练配置：{self.available_datasets}个数据集，{self.target_epochs}轮训练",
            f"单模型预估训练时间：{base_metrics['time_per_model_target_hours']:.1f}小时（{base_metrics['time_per_model_target_days']:.1f}天）",
            f"单通道方案总时间：{self.total_channels * base_metrics['time_per_model_target_days']:.0f}天（约{self.total_channels * base_metrics['time_per_model_target_days']/365:.1f}年）"
        ])

        # 成本对比分析
        best_scenario = min(scenarios.items(), key=lambda x: x[1]['total_parallel_time_days'])
        worst_scenario = max(scenarios.items(), key=lambda x: x[1]['total_parallel_time_days'])

        insights['cost_comparison'].extend([
            f"最优方案：{best_scenario[0]}，训练时间{best_scenario[1]['total_parallel_time_days']:.1f}天",
            f"最差方案：{worst_scenario[0]}，训练时间{worst_scenario[1]['total_parallel_time_days']:.1f}天",
            f"最大时间节省：{best_scenario[1]['time_saving_vs_single_channel']*100:.1f}%",
            f"效率提升倍数：{worst_scenario[1]['total_parallel_time_days']/best_scenario[1]['total_parallel_time_days']:.1f}倍"
        ])

        # 详细方案分析
        for name, data in scenarios.items():
            insights['cost_comparison'].append(
                f"{name}：{data['model_count']}个模型，{data['total_parallel_time_days']:.1f}天，"
                f"节省{data['time_saving_vs_single_channel']*100:.1f}%时间"
            )

        # 推荐建议
        insights['recommendations'].extend([
            "推荐方案排序（按训练效率）：",
            "1. 方向性聚类方案：3个模型，训练时间最短，物理基础最强",
            "2. 位置聚类方案：4个模型，平衡效率和复杂度",
            "3. 混合聚类方案：10个模型，灵活性最高",
            "考虑GPU资源：建议采用多GPU并行训练加速",
            "分阶段实施：先验证小规模方案，再扩展到全量"
        ])

        # 风险分析
        insights['risk_analysis'].extend([
            "超级通用模型风险：单点故障，训练复杂度极高",
            "方向性聚类风险：需要验证跨位置泛化能力",
            "并行训练风险：需要足够的GPU资源支持",
            "数据质量风险：通用模型对数据质量要求更高",
            "时间估算风险：实际训练时间可能因模型复杂度而变化±30%"
        ])

        self.cost_analysis['insights'] = insights
        return insights

    def save_results(self):
        """保存分析结果"""
        import json

        def convert_to_serializable(obj):
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif pd.isna(obj):
                return None
            else:
                return obj

        serializable_results = convert_to_serializable(self.cost_analysis)

        with open(os.path.join(self.output_dir, 'training_cost_analysis.json'), 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        print(f"训练成本分析结果已保存到 {self.output_dir}")

def main():
    """主函数"""
    output_dir = "results/training_cost_analysis"

    # 创建分析器
    analyzer = TrainingCostAnalyzer(output_dir)

    # 执行分析
    analyzer.analyze_clustering_scenarios()
    analyzer.create_cost_visualizations()
    insights = analyzer.generate_cost_insights()
    analyzer.save_results()

    # 打印关键洞察
    print("\n" + "="*60)
    print("模型训练时间成本分析结果")
    print("="*60)

    print("\n训练基准:")
    for baseline in insights['training_baseline']:
        print(f"• {baseline}")

    print("\n成本对比:")
    for comparison in insights['cost_comparison']:
        print(f"• {comparison}")

    print("\n推荐建议:")
    for rec in insights['recommendations']:
        print(f"• {rec}")

    print("\n风险分析:")
    for risk in insights['risk_analysis']:
        print(f"• {risk}")

    print(f"\n分析完成，详细结果已保存到 {output_dir}")

if __name__ == "__main__":
    main()
