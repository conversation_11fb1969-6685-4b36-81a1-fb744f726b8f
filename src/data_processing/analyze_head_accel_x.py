# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-14
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

# 头部X方向加速度数据分析脚本
# 用于分析碰撞测试中头部X方向加速度数据的统计特征和分布情况，
# 包括数据分布可视化、统计信息计算、数据标准化等功能。
# 主要用于数据质量评估和异常检测模型训练前的数据理解。

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
import os

# 设置输出目录
output_dir = 'processed_data'
os.makedirs(output_dir, exist_ok=True)

# 加载合并后的数据集
print("加载数据集...")
data_file = os.path.join(output_dir, 'all_Head_Acceleration_X.csv')
df = pd.read_csv(data_file)

# 显示数据集的基本信息
print("\n数据集基本信息:")
print(f"数据集形状: {df.shape}")
print(f"测试ID数量: {df['test_id'].nunique()}")
print("\n数据集前5行:")
print(df.head())

# 计算基本统计信息
print("\n基本统计信息:")
stats = df['value'].describe()
print(stats)

# 绘制数据分布直方图
plt.figure(figsize=(12, 6))
sns.histplot(df['value'], bins=100, kde=True)
plt.title('Head Acceleration X 数据分布')
plt.xlabel('加速度值 (m/s²)')
plt.ylabel('频率')
plt.grid(True, alpha=0.3)
plt.savefig(os.path.join(output_dir, 'head_accel_x_distribution.png'))
print(f"已保存数据分布图表到 {os.path.join(output_dir, 'head_accel_x_distribution.png')}")

# 按测试ID分组计算统计信息
print("\n按测试ID计算统计信息...")
group_stats = df.groupby('test_id')['value'].agg(['mean', 'std', 'min', 'max']).reset_index()
print("\n各测试ID的统计信息前10行:")
print(group_stats.head(10))

# 保存统计信息
group_stats.to_csv(os.path.join(output_dir, 'head_accel_x_stats_by_test.csv'), index=False)
print(f"已保存各测试ID的统计信息到 {os.path.join(output_dir, 'head_accel_x_stats_by_test.csv')}")

# 绘制箱线图，显示不同测试ID的数据分布
plt.figure(figsize=(15, 8))
# 随机选择10个测试ID进行可视化
sample_ids = np.random.choice(df['test_id'].unique(), 10, replace=False)
sample_df = df[df['test_id'].isin(sample_ids)]
sns.boxplot(x='test_id', y='value', data=sample_df)
plt.title('不同测试ID的Head Acceleration X分布')
plt.xlabel('测试ID')
plt.ylabel('加速度值 (m/s²)')
plt.xticks(rotation=90)
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'head_accel_x_boxplot.png'))
print(f"已保存箱线图到 {os.path.join(output_dir, 'head_accel_x_boxplot.png')}")

# 标准化数据
print("\n标准化数据...")
scaler = StandardScaler()
df_sample = df.sample(n=min(100000, len(df)), random_state=42)  # 随机抽样以加快处理速度
df_sample['value_scaled'] = scaler.fit_transform(df_sample[['value']])

# 绘制标准化后的数据分布
plt.figure(figsize=(12, 6))
sns.histplot(df_sample['value_scaled'], bins=100, kde=True)
plt.title('标准化后的Head Acceleration X数据分布')
plt.xlabel('标准化加速度值')
plt.ylabel('频率')
plt.grid(True, alpha=0.3)
plt.savefig(os.path.join(output_dir, 'head_accel_x_scaled_distribution.png'))
print(f"已保存标准化数据分布图表到 {os.path.join(output_dir, 'head_accel_x_scaled_distribution.png')}")

print("\n分析完成!")
