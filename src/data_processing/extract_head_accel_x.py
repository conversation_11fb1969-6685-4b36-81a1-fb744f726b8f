# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-14
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

# 头部X方向加速度数据提取脚本
# 从原始碰撞测试数据集中提取头部X方向加速度通道数据，
# 解析通道配置文件(.chn)和数据文件，将时间序列数据转换为CSV格式。
# 支持批量处理多个测试数据集，并生成合并的数据文件用于后续分析。

import os
import re
import pandas as pd
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt

# 定义数据集根目录
data_root = 'test-dataset'
# 定义处理后数据的保存目录
processed_data_dir = 'processed_data'
os.makedirs(processed_data_dir, exist_ok=True)

# 定义要提取的通道名称
target_channel_name = 'Head Acceleration X'
target_channel_code = '11HEAD0000H3ACXP'  # 这是第一个头部加速度X通道的代码

# 定义一个函数来解析通道文件
def parse_channel_file(file_path):
    """解析通道数据文件，提取元数据和时间序列数据"""
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 提取元数据
    metadata = {}
    data_start_idx = 0
    for i, line in enumerate(lines):
        if ':' in line:
            key, value = line.strip().split(':', 1)
            metadata[key.strip()] = value.strip()
        else:
            # 找到数据开始的行
            data_start_idx = i
            break

    # 提取时间序列数据
    time_series_data = []
    for line in lines[data_start_idx:]:
        try:
            value = float(line.strip())
            time_series_data.append(value)
        except ValueError:
            # 跳过无法转换为浮点数的行
            continue

    return metadata, np.array(time_series_data)

# 定义一个函数来查找并提取所有数据集中的目标通道数据
def extract_target_channel_data():
    """遍历所有数据集，提取目标通道的数据"""
    all_datasets = []

    # 遍历所有测试数据文件夹
    for test_dir in tqdm(os.listdir(data_root)):
        test_path = os.path.join(data_root, test_dir)
        if not os.path.isdir(test_path):
            continue

        # 遍历测试数据文件夹下的所有子文件夹
        for subdir in os.listdir(test_path):
            subdir_path = os.path.join(test_path, subdir)
            if not os.path.isdir(subdir_path):
                continue

            # 查找CHANNEL文件夹
            channel_dir = os.path.join(subdir_path, 'CHANNEL')
            if not os.path.exists(channel_dir):
                continue

            # 查找通道配置文件(.chn)
            chn_files = [f for f in os.listdir(channel_dir) if f.endswith('.chn')]
            if not chn_files:
                continue

            chn_file = os.path.join(channel_dir, chn_files[0])

            # 解析通道配置文件，找到目标通道的编号
            target_channel_num = None
            with open(chn_file, 'r') as f:
                for line in f:
                    if target_channel_name in line or target_channel_code in line:
                        match = re.search(r'Name of channel (\d+)', line)
                        if match:
                            target_channel_num = match.group(1)
                            break

            if not target_channel_num:
                print(f"未在 {chn_file} 中找到目标通道")
                continue

            # 构建目标通道文件的路径
            base_name = os.path.splitext(chn_files[0])[0]
            channel_file = os.path.join(channel_dir, f"{base_name}.{target_channel_num.zfill(3)}")

            if not os.path.exists(channel_file):
                print(f"未找到通道文件: {channel_file}")
                continue

            # 解析通道数据文件
            try:
                metadata, time_series_data = parse_channel_file(channel_file)

                # 创建数据集记录
                dataset = {
                    'test_id': subdir,
                    'channel_name': target_channel_name,
                    'channel_code': metadata.get('Channel code', ''),
                    'sampling_interval': float(metadata.get('Sampling interval', 0)),
                    'time_of_first_sample': float(metadata.get('Time of first sample', 0)),
                    'unit': metadata.get('Unit', ''),
                    'data': time_series_data
                }

                all_datasets.append(dataset)
                print(f"成功提取 {subdir} 的 {target_channel_name} 数据")
            except Exception as e:
                print(f"处理 {channel_file} 时出错: {str(e)}")

    return all_datasets

# 主函数
def main():
    print("开始提取目标通道数据...")
    datasets = extract_target_channel_data()

    if not datasets:
        print("未找到任何目标通道数据")
        return

    print(f"成功提取了 {len(datasets)} 个数据集的目标通道数据")

    # 将数据保存为CSV文件
    for i, dataset in enumerate(datasets):
        # 创建时间序列
        time_array = np.arange(
            dataset['time_of_first_sample'],
            dataset['time_of_first_sample'] + len(dataset['data']) * dataset['sampling_interval'],
            dataset['sampling_interval']
        )

        # 创建DataFrame
        df = pd.DataFrame({
            'time': time_array[:len(dataset['data'])],  # 确保时间和数据长度一致
            'value': dataset['data']
        })

        # 保存为CSV
        output_file = os.path.join(processed_data_dir, f"{dataset['test_id']}_{target_channel_name.replace(' ', '_')}.csv")
        df.to_csv(output_file, index=False)
        print(f"已保存数据集 {i+1}/{len(datasets)} 到 {output_file}")

    # 将所有数据合并为一个大的数据集
    print("合并所有数据集...")
    all_data = []
    for dataset in datasets:
        time_array = np.arange(
            dataset['time_of_first_sample'],
            dataset['time_of_first_sample'] + len(dataset['data']) * dataset['sampling_interval'],
            dataset['sampling_interval']
        )[:len(dataset['data'])]

        df = pd.DataFrame({
            'test_id': dataset['test_id'],
            'time': time_array,
            'value': dataset['data']
        })
        all_data.append(df)

    combined_df = pd.concat(all_data, ignore_index=True)
    combined_output_file = os.path.join(processed_data_dir, f"all_{target_channel_name.replace(' ', '_')}.csv")
    combined_df.to_csv(combined_output_file, index=False)
    print(f"已保存合并数据集到 {combined_output_file}")

    # 绘制一些数据样本进行可视化
    plt.figure(figsize=(15, 10))
    for i, dataset in enumerate(datasets[:5]):  # 只绘制前5个数据集
        time_array = np.arange(
            dataset['time_of_first_sample'],
            dataset['time_of_first_sample'] + len(dataset['data']) * dataset['sampling_interval'],
            dataset['sampling_interval']
        )[:len(dataset['data'])]

        plt.subplot(5, 1, i+1)
        plt.plot(time_array, dataset['data'])
        plt.title(f"测试ID: {dataset['test_id']}")
        plt.xlabel('时间 (s)')
        plt.ylabel(f"{target_channel_name} ({dataset['unit']})")

    plt.tight_layout()
    plt.savefig(os.path.join(processed_data_dir, f"{target_channel_name.replace(' ', '_')}_samples.png"))
    print(f"已保存数据样本可视化图表")

if __name__ == "__main__":
    main()
