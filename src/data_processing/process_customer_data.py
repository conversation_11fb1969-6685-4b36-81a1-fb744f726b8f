# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 客户异常数据处理与异常检测脚本
# 从客户提供的异常验证数据集中提取头部X方向加速度数据，
# 使用训练好的简单自编码器和LSTM自编码器模型进行异常检测。
# 支持批量处理多个测试数据集，生成异常检测结果和可视化报告。
# 主要用于验证模型在真实异常数据上的检测效果。

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import joblib
import tensorflow as tf
from tqdm import tqdm
import argparse
import glob
import re

# 设置目录
model_dir = 'models'
results_dir = 'results'
customer_data_dir = '客户异常数据-验证模型有效性使用'
output_dir = 'customer_data_results'
os.makedirs(output_dir, exist_ok=True)

# 定义参数
SEQUENCE_LENGTH = 100  # 序列长度
STRIDE = 10  # 滑动窗口步长

def read_channel_file(file_path):
    """
    读取通道数据文件

    参数:
    file_path: 文件路径

    返回:
    data: 包含时间和值的DataFrame
    """
    # 读取文件内容
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 提取元数据
    metadata = {}
    data_start_line = 0

    for i, line in enumerate(lines):
        if ':' in line:
            key, value = line.strip().split(':', 1)
            metadata[key.strip()] = value.strip()
        else:
            data_start_line = i
            break

    # 提取数据值
    values = []
    for i in range(data_start_line, len(lines)):
        line = lines[i].strip()
        if line:
            try:
                values.append(float(line))
            except ValueError:
                print(f"警告: 无法解析行 {i+1}: '{line}'")

    # 创建时间序列
    time_start = float(metadata.get('Time of first sample', '0'))
    num_samples = int(metadata.get('Number of samples', str(len(values))))
    time_step = 0.0001  # 假设采样率为10kHz

    times = np.arange(time_start, time_start + num_samples * time_step, time_step)[:len(values)]

    # 创建DataFrame
    df = pd.DataFrame({
        'time': times,
        'value': values
    })

    # 添加测试ID
    test_id = os.path.basename(file_path)
    df['test_id'] = test_id

    return df

def process_customer_data():
    """
    处理客户异常数据

    返回:
    all_data: 包含所有数据的DataFrame
    """
    all_data = []

    # 遍历所有子目录
    for subdir in tqdm(sorted(os.listdir(customer_data_dir)), desc="处理客户数据"):
        subdir_path = os.path.join(customer_data_dir, subdir)

        if os.path.isdir(subdir_path):
            channel_dir = os.path.join(subdir_path, 'CHANNEL')

            if os.path.exists(channel_dir):
                # 查找头部X方向加速度通道文件
                head_accel_x_files = []

                # 首先读取通道定义文件
                chn_files = glob.glob(os.path.join(channel_dir, '*.chn'))

                if chn_files:
                    chn_file = chn_files[0]
                    with open(chn_file, 'r') as f:
                        chn_content = f.read()

                    # 查找头部X方向加速度通道
                    head_accel_x_channels = []
                    for line in chn_content.split('\n'):
                        if 'Head Acceleration X' in line:
                            match = re.search(r'Name of channel (\d+)', line)
                            if match:
                                channel_num = match.group(1)
                                head_accel_x_channels.append(channel_num)

                    # 查找对应的数据文件
                    for channel_num in head_accel_x_channels:
                        channel_file = os.path.join(channel_dir, f"{subdir}.{channel_num.zfill(3)}")
                        if os.path.exists(channel_file):
                            head_accel_x_files.append(channel_file)

                # 处理找到的头部X方向加速度通道文件
                for file_path in head_accel_x_files:
                    try:
                        df = read_channel_file(file_path)
                        all_data.append(df)
                        print(f"成功处理文件: {file_path}")
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {str(e)}")

    # 合并所有数据
    if all_data:
        all_data_df = pd.concat(all_data, ignore_index=True)

        # 保存处理后的数据
        output_file = os.path.join(output_dir, 'all_customer_Head_Acceleration_X.csv')
        all_data_df.to_csv(output_file, index=False)
        print(f"所有客户数据已保存到: {output_file}")

        return all_data_df
    else:
        print("未找到任何头部X方向加速度数据")
        return None

def preprocess_data_for_inference(data, scaler, model_type='simple'):
    """
    预处理数据用于推理

    参数:
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    model_type: 模型类型，'simple'或'lstm'

    返回:
    X: 预处理后的输入数据
    sequence_indices: 序列的起始索引
    """
    # 标准化数据
    values_scaled = scaler.transform(data[['value']])
    data['value_scaled'] = values_scaled.flatten()

    # 创建序列数据
    sequences = []
    sequence_indices = []

    # 按测试ID分组处理
    for test_id, group in tqdm(data.groupby('test_id'), desc="创建序列"):
        values = group['value_scaled'].values

        # 使用滑动窗口创建序列
        for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
            seq = values[i:i+SEQUENCE_LENGTH]
            sequences.append(seq)
            sequence_indices.append(i)

    # 转换为numpy数组
    X = np.array(sequences)

    # 根据模型类型调整输入形状
    if model_type == 'lstm':
        X = X.reshape(X.shape[0], X.shape[1], 1)  # 重塑为(样本数, 时间步, 特征数)

    return X, sequence_indices

def detect_anomalies(model, data, scaler, threshold, model_type='simple'):
    """
    检测异常

    参数:
    model: 自编码器模型
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    threshold: 异常检测阈值
    model_type: 模型类型，'simple'或'lstm'

    返回:
    anomalies: 包含异常标记的DataFrame
    """
    # 预处理数据
    X, sequence_indices = preprocess_data_for_inference(data, scaler, model_type)

    # 使用模型进行预测
    X_pred = model.predict(X)

    # 计算重构误差
    if model_type == 'lstm':
        errors = np.mean(np.square(X - X_pred), axis=(1, 2))
    else:
        errors = np.mean(np.square(X - X_pred), axis=1)

    # 标记异常
    anomalies = pd.DataFrame({
        'start_idx': sequence_indices,
        'end_idx': [i + SEQUENCE_LENGTH - 1 for i in sequence_indices],
        'reconstruction_error': errors,
        'is_anomaly': errors > threshold
    })

    return anomalies

def plot_anomalies(data, anomalies, output_file):
    """
    绘制异常检测结果

    参数:
    data: 原始数据
    anomalies: 异常检测结果
    output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始时间序列
    plt.plot(data['time'], data['value'], label='Original Data')

    # 标记异常区域
    anomaly_sequences = anomalies[anomalies['is_anomaly']]
    for _, row in anomaly_sequences.iterrows():
        start_idx = row['start_idx']
        end_idx = row['end_idx']
        if start_idx < len(data['time']) and end_idx < len(data['time']):
            plt.axvspan(data['time'].iloc[start_idx], data['time'].iloc[end_idx],
                       alpha=0.3, color='red')

    plt.title('Anomaly Detection Results')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def main():
    # 处理客户数据
    customer_data = process_customer_data()

    if customer_data is None:
        print("未能处理客户数据，退出")
        return

    # 加载模型和阈值
    # 简单自编码器
    simple_model = tf.keras.models.load_model(os.path.join(model_dir, 'simple_autoencoder.h5'), compile=False)
    simple_model.compile(optimizer='adam', loss='mse')
    simple_threshold = np.load(os.path.join(model_dir, 'simple_autoencoder_threshold.npy'))
    simple_scaler = joblib.load(os.path.join(model_dir, 'simple_autoencoder_scaler.pkl'))

    # LSTM自编码器
    lstm_model = tf.keras.models.load_model(os.path.join(model_dir, 'lstm_autoencoder.h5'), compile=False)
    lstm_model.compile(optimizer='adam', loss='mse')
    lstm_threshold = np.load(os.path.join(model_dir, 'lstm_threshold.npy'))
    lstm_scaler = joblib.load(os.path.join(model_dir, 'lstm_scaler.pkl'))

    # 按测试ID分组进行异常检测
    for test_id, group in customer_data.groupby('test_id'):
        print(f"处理测试ID: {test_id}")

        # 使用简单自编码器检测异常
        print("使用简单自编码器检测异常...")
        simple_anomalies = detect_anomalies(simple_model, group, simple_scaler, simple_threshold, 'simple')

        # 使用LSTM自编码器检测异常
        print("使用LSTM自编码器检测异常...")
        lstm_anomalies = detect_anomalies(lstm_model, group, lstm_scaler, lstm_threshold, 'lstm')

        # 统计异常
        simple_anomaly_count = simple_anomalies['is_anomaly'].sum()
        simple_total_sequences = len(simple_anomalies)
        simple_anomaly_percentage = (simple_anomaly_count / simple_total_sequences) * 100

        lstm_anomaly_count = lstm_anomalies['is_anomaly'].sum()
        lstm_total_sequences = len(lstm_anomalies)
        lstm_anomaly_percentage = (lstm_anomaly_count / lstm_total_sequences) * 100

        print(f"简单自编码器: 检测到 {simple_anomaly_count} 个异常序列，占总序列的 {simple_anomaly_percentage:.2f}%")
        print(f"LSTM自编码器: 检测到 {lstm_anomaly_count} 个异常序列，占总序列的 {lstm_anomaly_percentage:.2f}%")

        # 保存异常检测结果
        simple_result_file = os.path.join(output_dir, f"{test_id}_simple_anomalies.csv")
        simple_anomalies.to_csv(simple_result_file, index=False)
        print(f"简单自编码器异常检测结果已保存到: {simple_result_file}")

        lstm_result_file = os.path.join(output_dir, f"{test_id}_lstm_anomalies.csv")
        lstm_anomalies.to_csv(lstm_result_file, index=False)
        print(f"LSTM自编码器异常检测结果已保存到: {lstm_result_file}")

        # 绘制异常检测结果
        simple_plot_file = os.path.join(output_dir, f"{test_id}_simple_anomalies.png")
        plot_anomalies(group, simple_anomalies, simple_plot_file)
        print(f"简单自编码器异常检测可视化已保存到: {simple_plot_file}")

        lstm_plot_file = os.path.join(output_dir, f"{test_id}_lstm_anomalies.png")
        plot_anomalies(group, lstm_anomalies, lstm_plot_file)
        print(f"LSTM自编码器异常检测可视化已保存到: {lstm_plot_file}")

    # 生成综合报告
    generate_summary_report(customer_data)

def generate_summary_report(customer_data):
    """
    生成综合报告

    参数:
    customer_data: 客户数据
    """
    # 创建报告目录
    report_dir = os.path.join(output_dir, 'report')
    os.makedirs(report_dir, exist_ok=True)

    # 读取所有异常检测结果
    simple_results = {}
    lstm_results = {}

    for test_id in customer_data['test_id'].unique():
        simple_file = os.path.join(output_dir, f"{test_id}_simple_anomalies.csv")
        lstm_file = os.path.join(output_dir, f"{test_id}_lstm_anomalies.csv")

        if os.path.exists(simple_file):
            simple_anomalies = pd.read_csv(simple_file)
            simple_results[test_id] = {
                'anomaly_count': simple_anomalies['is_anomaly'].sum(),
                'total_sequences': len(simple_anomalies),
                'anomaly_percentage': (simple_anomalies['is_anomaly'].sum() / len(simple_anomalies)) * 100
            }

        if os.path.exists(lstm_file):
            lstm_anomalies = pd.read_csv(lstm_file)
            lstm_results[test_id] = {
                'anomaly_count': lstm_anomalies['is_anomaly'].sum(),
                'total_sequences': len(lstm_anomalies),
                'anomaly_percentage': (lstm_anomalies['is_anomaly'].sum() / len(lstm_anomalies)) * 100
            }

    # 创建综合报告
    report = []
    report.append("# 客户异常数据检测报告\n")
    report.append("## 简单自编码器检测结果\n")
    report.append("| 测试ID | 异常序列数 | 总序列数 | 异常百分比 |\n")
    report.append("|--------|------------|----------|------------|\n")

    for test_id, result in simple_results.items():
        report.append(f"| {test_id} | {result['anomaly_count']} | {result['total_sequences']} | {result['anomaly_percentage']:.2f}% |\n")

    report.append("\n## LSTM自编码器检测结果\n")
    report.append("| 测试ID | 异常序列数 | 总序列数 | 异常百分比 |\n")
    report.append("|--------|------------|----------|------------|\n")

    for test_id, result in lstm_results.items():
        report.append(f"| {test_id} | {result['anomaly_count']} | {result['total_sequences']} | {result['anomaly_percentage']:.2f}% |\n")

    # 保存报告
    report_file = os.path.join(report_dir, 'anomaly_detection_report.md')
    with open(report_file, 'w') as f:
        f.writelines(report)

    print(f"综合报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
