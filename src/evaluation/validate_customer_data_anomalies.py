# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-20
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 客户数据异常检测验证脚本
# 验证训练好的异常检测模型在9个客户数据集上对Head Acceleration X通道(001)的异常检测性能，
# 支持多种模型类型（简单自编码器、LSTM自编码器、高级LSTM自编码器、孤立森林），
# 生成详细的异常检测结果、可视化图表和性能评估报告。

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import tensorflow as tf
from tqdm import tqdm
import argparse
import glob
import re
import subprocess
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
from datetime import datetime

# 设置目录
data_dir = 'data/customer'
model_dir = 'models'
results_dir = 'customer_validation_results'
os.makedirs(results_dir, exist_ok=True)

# 设置日志
print(f"开始执行时间: {datetime.now()}")
print(f"当前工作目录: {os.getcwd()}")
print(f"数据目录: {data_dir}")
print(f"模型目录: {model_dir}")
print(f"结果目录: {results_dir}")

# 定义参数
SEQUENCE_LENGTH = 100  # 序列长度
STRIDE = 10  # 滑动窗口步长

def read_channel_file(file_path):
    """
    读取通道数据文件

    参数:
    file_path: 文件路径

    返回:
    data: 包含时间和值的DataFrame
    """
    # 读取文件内容
    with open(file_path, 'r') as f:
        lines = f.readlines()

    # 提取元数据
    metadata = {}
    data_start_line = 0

    for i, line in enumerate(lines):
        if ':' in line:
            key, value = line.strip().split(':', 1)
            metadata[key.strip()] = value.strip()
        else:
            data_start_line = i
            break

    # 提取数据值
    values = []
    for i in range(data_start_line, len(lines)):
        line = lines[i].strip()
        if line:
            try:
                values.append(float(line))
            except ValueError:
                print(f"警告: 无法解析行 {i+1}: '{line}'")

    # 创建时间序列
    time_start = float(metadata.get('Time of first sample', '0'))
    num_samples = int(metadata.get('Number of samples', str(len(values))))
    time_step = 0.0001  # 假设采样率为10kHz

    times = np.arange(time_start, time_start + num_samples * time_step, time_step)[:len(values)]

    # 创建DataFrame
    df = pd.DataFrame({
        'time': times,
        'value': values
    })

    # 添加测试ID
    test_id = os.path.basename(file_path)
    df['test_id'] = test_id

    return df

def process_customer_data():
    """
    处理9个客户数据集中的Head Acceleration X通道数据

    返回:
    all_data: 包含所有数据的DataFrame，按数据集分组
    """
    all_data = []
    customer_data_dir = 'data/customer'

    # 遍历9个客户数据集
    for subdir in tqdm(sorted(os.listdir(customer_data_dir)), desc="处理客户数据集"):
        subdir_path = os.path.join(customer_data_dir, subdir)

        if os.path.isdir(subdir_path):
            channel_dir = os.path.join(subdir_path, 'CHANNEL')

            if os.path.exists(channel_dir):
                # 查找Head Acceleration X通道文件 (*.001)
                head_accel_x_file = os.path.join(channel_dir, f"{subdir}.001")

                if os.path.exists(head_accel_x_file):
                    try:
                        df = read_channel_file(head_accel_x_file)
                        # 添加数据集ID
                        df['dataset_id'] = subdir
                        all_data.append(df)
                        print(f"成功处理数据集 {subdir} 的Head Acceleration X通道({subdir}.001)数据")
                    except Exception as e:
                        print(f"处理数据集 {subdir} 的Head Acceleration X通道({subdir}.001)数据时出错: {str(e)}")
                else:
                    print(f"数据集 {subdir} 中未找到Head Acceleration X通道({subdir}.001)数据")

    # 合并所有数据
    if all_data:
        all_data_df = pd.concat(all_data, ignore_index=True)

        # 保存处理后的数据
        output_file = os.path.join(results_dir, 'all_customer_Head_Acceleration_X.csv')
        all_data_df.to_csv(output_file, index=False)
        print(f"所有客户数据已保存到: {output_file}")

        return all_data_df
    else:
        print("未找到任何Head Acceleration X通道数据")
        return None

def preprocess_data_for_inference(data, scaler, model_type='simple'):
    """
    预处理数据用于推理

    参数:
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    model_type: 模型类型，'simple'、'lstm'或'advanced_lstm'

    返回:
    X: 预处理后的输入数据
    sequence_indices: 序列的起始索引
    """
    # 标准化数据
    values_scaled = scaler.transform(data[['value']])
    data['value_scaled'] = values_scaled.flatten()

    # 创建序列数据
    sequences = []
    sequence_indices = []

    # 按数据集ID分组处理
    for dataset_id, group in tqdm(data.groupby('dataset_id'), desc="创建序列"):
        values = group['value_scaled'].values

        # 使用滑动窗口创建序列
        for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
            seq = values[i:i+SEQUENCE_LENGTH]
            sequences.append(seq)
            sequence_indices.append(i)

    # 转换为numpy数组
    X = np.array(sequences)

    # 根据模型类型调整输入形状
    if model_type == 'lstm' or model_type == 'advanced_lstm':
        X = X.reshape(X.shape[0], X.shape[1], 1)  # 重塑为(样本数, 时间步, 特征数)

    return X, sequence_indices

def detect_anomalies(model, data, scaler, threshold, model_type='simple'):
    """
    检测异常

    参数:
    model: 模型（自编码器或孤立森林）
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象
    threshold: 异常检测阈值
    model_type: 模型类型，'simple'、'lstm'、'advanced_lstm'或'isolation_forest'

    返回:
    anomalies: 包含异常标记的DataFrame
    """
    # 处理孤立森林模型
    if model_type == 'isolation_forest':
        return detect_anomalies_isolation_forest(model, data, scaler)

    # 预处理数据
    X, sequence_indices = preprocess_data_for_inference(data, scaler, model_type)

    # 使用模型进行预测
    X_pred = model.predict(X)

    # 计算重构误差
    print(f"输入形状: {X.shape}, 输出形状: {X_pred.shape}")

    if model_type == 'lstm' or model_type == 'advanced_lstm':
        # 对于LSTM和高级LSTM自编码器，输入和输出应该都是3D的
        if len(X_pred.shape) == 3 and X.shape == X_pred.shape:
            # 标准自编码器情况：输入和输出形状相同
            errors = np.mean(np.square(X - X_pred), axis=(1, 2))
            print(f"使用标准重构误差计算方法，输入和输出形状相同: {X.shape}")
        elif len(X_pred.shape) == 2:
            # 如果使用的是编码器而不是自编码器，输出是2D的潜在表示
            print(f"警告: 检测到使用的是编码器而不是完整的自编码器")
            print(f"尝试加载完整的自编码器模型: advanced_{model_type}_autoencoder.h5")

            try:
                # 尝试加载完整的自编码器模型
                full_model_path = os.path.join(model_dir, f'advanced_{model_type}_autoencoder.h5')
                if os.path.exists(full_model_path):
                    full_model = tf.keras.models.load_model(full_model_path, compile=False)
                    full_model.compile(optimizer='adam', loss='mse')
                    # 使用完整模型进行预测
                    X_pred = full_model.predict(X)
                    errors = np.mean(np.square(X - X_pred), axis=(1, 2))
                    print(f"成功加载并使用完整的自编码器模型计算重构误差")
                else:
                    # 如果找不到完整模型，使用替代方法
                    print(f"未找到完整的自编码器模型，使用替代方法计算误差")
                    X_flat = X.reshape(X.shape[0], -1)
                    X_mean = np.mean(X_flat, axis=1)
                    X_pred_mean = np.mean(X_pred, axis=1)
                    errors = np.abs(X_mean - X_pred_mean)
            except Exception as e:
                print(f"加载完整模型时出错: {str(e)}，使用替代方法计算误差")
                X_flat = X.reshape(X.shape[0], -1)
                X_mean = np.mean(X_flat, axis=1)
                X_pred_mean = np.mean(X_pred, axis=1)
                errors = np.abs(X_mean - X_pred_mean)
        else:
            # 其他情况，使用标准方法
            print(f"使用标准重构误差计算方法，但输入和输出形状不同")
            errors = np.mean(np.square(X.reshape(X.shape[0], -1) - X_pred.reshape(X_pred.shape[0], -1)), axis=1)
    else:
        # 对于简单自编码器，输入和输出是2D的
        errors = np.mean(np.square(X - X_pred), axis=1)
        print(f"使用简单自编码器重构误差计算方法")

    # 标记异常
    anomalies = pd.DataFrame({
        'start_idx': sequence_indices,
        'end_idx': [i + SEQUENCE_LENGTH - 1 for i in sequence_indices],
        'reconstruction_error': errors,
        'is_anomaly': errors > threshold
    })

    return anomalies

def extract_features(sequence):
    """
    从序列中提取特征

    参数:
    sequence: 输入序列

    返回:
    features: 提取的特征
    """
    features = [
        np.mean(sequence),  # 均值
        np.std(sequence),   # 标准差
        np.min(sequence),   # 最小值
        np.max(sequence),   # 最大值
        np.median(sequence),  # 中位数
        np.percentile(sequence, 25),  # 25分位数
        np.percentile(sequence, 75),  # 75分位数
        np.sum(np.diff(sequence) ** 2),  # 能量
        np.mean(np.abs(np.diff(sequence))),  # 平均绝对变化
        np.max(np.abs(np.diff(sequence)))  # 最大绝对变化
    ]
    return features

def preprocess_data_for_isolation_forest(data, scaler):
    """
    预处理数据用于孤立森林模型

    参数:
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象

    返回:
    X: 预处理后的输入数据
    sequence_indices: 序列的起始索引
    """
    # 创建序列数据
    sequences = []
    sequence_indices = []

    values = data['value'].values

    # 使用滑动窗口创建序列
    for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
        seq = values[i:i+SEQUENCE_LENGTH]
        # 检查序列是否包含NaN
        if np.isnan(seq).any():
            continue
        features = extract_features(seq)
        # 检查特征是否包含NaN
        if np.isnan(features).any():
            continue

        sequences.append(features)
        sequence_indices.append(i)

    if not sequences:
        print("错误: 提取特征后没有有效序列")
        return None, None

    # 转换为numpy数组
    X = np.array(sequences)

    # 检查是否有NaN值
    if np.isnan(X).any():
        print("警告: 数据中存在NaN值，将被移除")
        # 移除包含NaN的行
        mask = ~np.isnan(X).any(axis=1)
        X = X[mask]
        sequence_indices = [sequence_indices[i] for i in range(len(mask)) if mask[i]]
        if X.shape[0] == 0:
            print("错误: 移除NaN后没有剩余数据")
            return None, None

    # 标准化数据
    X_scaled = scaler.transform(X)

    return X_scaled, sequence_indices

def detect_anomalies_isolation_forest(model, data, scaler):
    """
    使用孤立森林模型检测异常

    参数:
    model: 孤立森林模型
    data: 包含时间和值的DataFrame
    scaler: 用于标准化的scaler对象

    返回:
    anomalies: 包含异常标记的DataFrame
    """
    # 预处理数据
    X, sequence_indices = preprocess_data_for_isolation_forest(data, scaler)

    # 使用模型进行预测
    scores = model.decision_function(X)  # 异常分数
    labels = model.predict(X)  # 预测标签（1表示正常，-1表示异常）

    # 标记异常
    anomalies = pd.DataFrame({
        'start_idx': sequence_indices,
        'end_idx': [i + SEQUENCE_LENGTH - 1 for i in sequence_indices],
        'reconstruction_error': -scores,  # 使用负的决策函数值作为"重构误差"，以保持一致的接口
        'is_anomaly': labels == -1
    })

    return anomalies

def plot_anomalies(data, anomalies, output_file):
    """
    绘制异常检测结果

    参数:
    data: 原始数据
    anomalies: 异常检测结果
    output_file: 输出文件路径
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始时间序列
    plt.plot(data['time'], data['value'], label='Original Data')

    # 标记异常区域
    anomaly_sequences = anomalies[anomalies['is_anomaly']]
    for _, row in anomaly_sequences.iterrows():
        start_idx = row['start_idx']
        end_idx = row['end_idx']
        if start_idx < len(data['time']) and end_idx < len(data['time']):
            plt.axvspan(data['time'].iloc[start_idx], data['time'].iloc[end_idx],
                       alpha=0.3, color='red')

    plt.title('Anomaly Detection Results')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def plot_error_distribution(errors, threshold, output_file):
    """
    绘制重构误差分布

    参数:
    errors: 重构误差数组
    threshold: 异常检测阈值
    output_file: 输出文件路径
    """
    plt.figure(figsize=(12, 6))

    sns.histplot(errors, kde=True)
    plt.axvline(threshold, color='r', linestyle='--', label=f'Threshold: {threshold:.4f}')

    plt.title('Reconstruction Error Distribution')
    plt.xlabel('Reconstruction Error')
    plt.ylabel('Frequency')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(output_file)
    plt.close()

def main():
    # 处理客户数据
    customer_data = process_customer_data()

    if customer_data is None:
        print("未能处理客户数据，退出")
        return

    # 加载模型和阈值
    models = {}

    # 检查可用的模型文件
    print("检查可用的模型文件...")

    # 检查高级LSTM自编码器模型
    advanced_lstm_autoencoder_path = os.path.join(model_dir, 'advanced_lstm_autoencoder.h5')
    advanced_lstm_encoder_path = os.path.join(model_dir, 'advanced_lstm_encoder.h5')

    if os.path.exists(advanced_lstm_autoencoder_path):
        print(f"找到完整的高级LSTM自编码器模型: {advanced_lstm_autoencoder_path}")
        try:
            # 尝试加载完整的自编码器模型
            advanced_lstm_model = tf.keras.models.load_model(advanced_lstm_autoencoder_path, compile=False)
            advanced_lstm_model.compile(optimizer='adam', loss='mse')

            # 检查阈值和scaler文件
            threshold_path = os.path.join(model_dir, 'advanced_lstm_threshold.npy')
            scaler_path = os.path.join(model_dir, 'advanced_lstm_scaler.pkl')

            if not os.path.exists(threshold_path):
                print(f"未找到高级LSTM自编码器阈值文件，将使用默认阈值")
                # 使用一个默认阈值
                threshold = 0.1
            else:
                threshold = np.load(threshold_path)

            if not os.path.exists(scaler_path):
                print(f"未找到高级LSTM自编码器scaler文件，将使用默认scaler")
                # 创建一个默认的scaler
                scaler = StandardScaler()
            else:
                scaler = joblib.load(scaler_path)

            models['advanced_lstm'] = {
                'model': advanced_lstm_model,
                'threshold': threshold,
                'scaler': scaler
            }
            print("成功加载高级LSTM自编码器模型")
        except Exception as e:
            print(f"加载高级LSTM自编码器模型时出错: {str(e)}")
    elif os.path.exists(advanced_lstm_encoder_path):
        print(f"未找到完整的高级LSTM自编码器模型，但找到了编码器模型: {advanced_lstm_encoder_path}")
        print(f"将使用编码器模型进行异常检测，但性能可能不如完整的自编码器")
        try:
            # 加载编码器模型
            advanced_lstm_encoder = tf.keras.models.load_model(advanced_lstm_encoder_path, compile=False)
            advanced_lstm_encoder.compile(optimizer='adam', loss='mse')

            # 检查阈值和scaler文件
            threshold_path = os.path.join(model_dir, 'advanced_lstm_threshold.npy')
            scaler_path = os.path.join(model_dir, 'advanced_lstm_scaler.pkl')

            if not os.path.exists(threshold_path):
                print(f"未找到高级LSTM自编码器阈值文件，将使用默认阈值")
                # 使用一个默认阈值
                threshold = 0.1
            else:
                threshold = np.load(threshold_path)

            if not os.path.exists(scaler_path):
                print(f"未找到高级LSTM自编码器scaler文件，将使用默认scaler")
                # 创建一个默认的scaler
                scaler = StandardScaler()
            else:
                scaler = joblib.load(scaler_path)

            models['advanced_lstm'] = {
                'model': advanced_lstm_encoder,
                'threshold': threshold,
                'scaler': scaler
            }
            print("成功加载高级LSTM编码器模型")
        except Exception as e:
            print(f"加载高级LSTM编码器模型时出错: {str(e)}")
    else:
        print("未找到高级LSTM自编码器或编码器模型")

    # 简单自编码器
    if os.path.exists(os.path.join(model_dir, 'simple_autoencoder.h5')) and \
       os.path.exists(os.path.join(model_dir, 'simple_autoencoder_threshold.npy')) and \
       os.path.exists(os.path.join(model_dir, 'simple_autoencoder_scaler.pkl')):
        print("加载简单自编码器模型...")
        models['simple'] = {
            'model': tf.keras.models.load_model(os.path.join(model_dir, 'simple_autoencoder.h5'), compile=False),
            'threshold': np.load(os.path.join(model_dir, 'simple_autoencoder_threshold.npy')),
            'scaler': joblib.load(os.path.join(model_dir, 'simple_autoencoder_scaler.pkl'))
        }
        models['simple']['model'].compile(optimizer='adam', loss='mse')
    else:
        print("简单自编码器模型文件不完整，跳过")

    # LSTM自编码器
    if os.path.exists(os.path.join(model_dir, 'lstm_autoencoder.h5')) and \
       os.path.exists(os.path.join(model_dir, 'lstm_threshold.npy')) and \
       os.path.exists(os.path.join(model_dir, 'lstm_scaler.pkl')):
        print("加载LSTM自编码器模型...")
        models['lstm'] = {
            'model': tf.keras.models.load_model(os.path.join(model_dir, 'lstm_autoencoder.h5'), compile=False),
            'threshold': np.load(os.path.join(model_dir, 'lstm_threshold.npy')),
            'scaler': joblib.load(os.path.join(model_dir, 'lstm_scaler.pkl'))
        }
        models['lstm']['model'].compile(optimizer='adam', loss='mse')
    else:
        print("LSTM自编码器模型文件不完整，跳过")

    # 高级LSTM自编码器 - 这部分代码已经在前面处理过，这里只是为了兼容性保留
    # 如果前面没有成功加载高级LSTM自编码器模型，尝试使用编码器模型
    if 'advanced_lstm' not in models and \
       os.path.exists(os.path.join(model_dir, 'advanced_lstm_encoder.h5')) and \
       os.path.exists(os.path.join(model_dir, 'advanced_lstm_threshold.npy')) and \
       os.path.exists(os.path.join(model_dir, 'advanced_lstm_scaler.pkl')):
        print("前面未能加载完整的高级LSTM自编码器模型，尝试使用编码器模型...")
        models['advanced_lstm'] = {
            'model': tf.keras.models.load_model(os.path.join(model_dir, 'advanced_lstm_encoder.h5'), compile=False),
            'threshold': np.load(os.path.join(model_dir, 'advanced_lstm_threshold.npy')),
            'scaler': joblib.load(os.path.join(model_dir, 'advanced_lstm_scaler.pkl'))
        }
        models['advanced_lstm']['model'].compile(optimizer='adam', loss='mse')
        print("成功加载高级LSTM编码器模型作为备用")
    elif 'advanced_lstm' not in models:
        print("高级LSTM自编码器模型文件不完整，跳过")

    # 孤立森林模型
    if os.path.exists(os.path.join(model_dir, 'isolation_forest.pkl')) and \
       os.path.exists(os.path.join(model_dir, 'scaler.pkl')):
        print("加载孤立森林模型...")
        models['isolation_forest'] = {
            'model': joblib.load(os.path.join(model_dir, 'isolation_forest.pkl')),
            'scaler': joblib.load(os.path.join(model_dir, 'scaler.pkl')),
            'threshold': 0  # 孤立森林不需要阈值，这里只是为了保持一致的接口
        }
        print("成功加载孤立森林模型")
    else:
        print("孤立森林模型文件不完整，跳过")

    # 检查是否有可用的模型
    if not models:
        print("没有可用的模型，退出")
        return

    # 按数据集ID分组进行异常检测
    results = {}

    # 获取可用的模型列表
    available_models = list(models.keys())
    print(f"可用的模型: {', '.join(available_models)}")

    for dataset_id, group in customer_data.groupby('dataset_id'):
        print(f"处理数据集: {dataset_id}")
        results[dataset_id] = {}

        # 为每个可用的模型进行异常检测
        for model_name in available_models:
            model_info = models[model_name]
            print(f"使用{model_name}模型检测异常...")

            # 检测异常
            anomalies = detect_anomalies(
                model_info['model'],
                group,
                model_info['scaler'],
                model_info['threshold'],
                model_name
            )

            # 统计异常
            anomaly_count = anomalies['is_anomaly'].sum()
            total_sequences = len(anomalies)
            anomaly_percentage = (anomaly_count / total_sequences) * 100

            print(f"{model_name}: 检测到 {anomaly_count} 个异常序列，占总序列的 {anomaly_percentage:.2f}%")

            # 保存结果
            results[dataset_id][model_name] = {
                'anomaly_count': anomaly_count,
                'total_sequences': total_sequences,
                'anomaly_percentage': anomaly_percentage,
                'anomalies': anomalies
            }

            # 保存异常检测结果
            result_file = os.path.join(results_dir, f"{dataset_id}_{model_name}_anomalies.csv")
            anomalies.to_csv(result_file, index=False)
            print(f"{model_name}异常检测结果已保存到: {result_file}")

            # 绘制异常检测结果
            plot_file = os.path.join(results_dir, f"{dataset_id}_{model_name}_anomalies.png")
            plot_anomalies(group, anomalies, plot_file)
            print(f"{model_name}异常检测可视化已保存到: {plot_file}")

            # 绘制重构误差分布
            error_dist_file = os.path.join(results_dir, f"{dataset_id}_{model_name}_error_distribution.png")
            plot_error_distribution(anomalies['reconstruction_error'], model_info['threshold'], error_dist_file)
            print(f"{model_name}重构误差分布已保存到: {error_dist_file}")

    # 生成综合报告
    generate_summary_report(results)

def generate_summary_report(results):
    """
    生成综合报告

    参数:
    results: 异常检测结果字典
    """
    # 创建报告目录
    report_dir = os.path.join(results_dir, 'report')
    os.makedirs(report_dir, exist_ok=True)

    # 获取可用的模型列表
    available_models = set()
    for dataset_id in results:
        for model_name in results[dataset_id]:
            available_models.add(model_name)
    available_models = sorted(list(available_models))

    # 创建综合报告
    report = []
    report.append("# Head Acceleration X通道(001)异常检测综合报告\n\n")

    # 添加摘要表格
    report.append("## 异常检测摘要\n\n")
    report.append("| 数据集ID | 模型类型 | 异常序列数 | 总序列数 | 异常百分比 |\n")
    report.append("|----------|----------|------------|----------|------------|\n")

    for dataset_id in sorted(results.keys()):
        for model_name in available_models:
            if model_name in results[dataset_id]:
                result = results[dataset_id][model_name]
                report.append(f"| {dataset_id} | {model_name} | {result['anomaly_count']} | {result['total_sequences']} | {result['anomaly_percentage']:.2f}% |\n")

    # 添加模型比较
    report.append("\n## 模型性能比较\n\n")

    # 计算每个模型的平均异常检测率
    model_avg_rates = {}
    for model_name in available_models:
        rates = []
        for dataset_id in results:
            if model_name in results[dataset_id]:
                rates.append(results[dataset_id][model_name]['anomaly_percentage'])
        if rates:
            model_avg_rates[model_name] = sum(rates) / len(rates)
        else:
            model_avg_rates[model_name] = 0

    report.append("| 模型类型 | 平均异常检测率 |\n")
    report.append("|----------|----------------|\n")
    for model_name, avg_rate in model_avg_rates.items():
        report.append(f"| {model_name} | {avg_rate:.2f}% |\n")

    # 添加数据集分析
    report.append("\n## 数据集异常分析\n\n")

    for dataset_id in sorted(results.keys()):
        report.append(f"### 数据集 {dataset_id}\n\n")

        # 添加各模型在该数据集上的性能比较
        report.append("| 模型类型 | 异常序列数 | 总序列数 | 异常百分比 |\n")
        report.append("|----------|------------|----------|------------|\n")

        for model_name in available_models:
            if model_name in results[dataset_id]:
                result = results[dataset_id][model_name]
                report.append(f"| {model_name} | {result['anomaly_count']} | {result['total_sequences']} | {result['anomaly_percentage']:.2f}% |\n")

        report.append("\n")

    # 添加结论和建议
    report.append("## 结论与建议\n\n")
    report.append("根据上述分析，我们得出以下结论：\n\n")

    # 找出性能最好的模型
    if model_avg_rates:
        best_model = max(model_avg_rates.items(), key=lambda x: x[1])[0]
        report.append(f"1. {best_model}模型在大多数数据集上表现最佳，平均异常检测率为{model_avg_rates[best_model]:.2f}%\n")

        # 如果有孤立森林模型，添加与其他模型的比较
        if 'isolation_forest' in model_avg_rates:
            isolation_forest_rate = model_avg_rates['isolation_forest']
            other_models = {model: rate for model, rate in model_avg_rates.items() if model != 'isolation_forest'}
            if other_models:
                best_other_model = max(other_models.items(), key=lambda x: x[1])[0]
                best_other_model_rate = other_models[best_other_model]

                if isolation_forest_rate > best_other_model_rate:
                    report.append(f"2. 孤立森林模型表现优于深度学习模型，平均异常检测率为{isolation_forest_rate:.2f}%，而{best_other_model}模型为{best_other_model_rate:.2f}%\n")
                else:
                    report.append(f"2. 深度学习模型({best_other_model})表现优于传统机器学习模型(孤立森林)，平均异常检测率为{best_other_model_rate:.2f}%，而孤立森林为{isolation_forest_rate:.2f}%\n")
                report.append("3. 不同数据集的异常特征有所不同，需要针对特定数据集选择合适的模型\n")
                report.append("4. 建议在实际应用中，结合多个模型的检测结果，提高异常检测的准确性\n")
            else:
                report.append("2. 不同数据集的异常特征有所不同，需要针对特定数据集选择合适的模型\n")
                report.append("3. 建议在实际应用中，结合多个模型的检测结果，提高异常检测的准确性\n")
        else:
            report.append("2. 不同数据集的异常特征有所不同，需要针对特定数据集选择合适的模型\n")
            report.append("3. 建议在实际应用中，结合多个模型的检测结果，提高异常检测的准确性\n")
    else:
        report.append("1. 不同数据集的异常特征有所不同，需要针对特定数据集选择合适的模型\n")
        report.append("2. 建议在实际应用中，结合多个模型的检测结果，提高异常检测的准确性\n")

    # 保存报告
    report_file = os.path.join(report_dir, 'anomaly_detection_comprehensive_report.md')
    with open(report_file, 'w') as f:
        f.writelines(report)

    print(f"综合报告已保存到: {report_file}")

    # 生成HTML报告
    generate_html_report(results, available_models)

def generate_html_report(results, available_models):
    """
    生成HTML格式的报告，用于更新网页

    参数:
    results: 异常检测结果字典
    available_models: 可用的模型列表
    """
    # 创建HTML报告目录
    html_report_dir = os.path.join(results_dir, 'html')
    os.makedirs(html_report_dir, exist_ok=True)

    # 创建HTML报告
    html = []
    html.append("<!DOCTYPE html>\n")
    html.append("<html lang=\"zh-CN\">\n")
    html.append("<head>\n")
    html.append("    <meta charset=\"UTF-8\">\n")
    html.append("    <title>Head Acceleration X通道(001)异常检测报告</title>\n")
    html.append("    <style>\n")
    html.append("        body { font-family: Arial, sans-serif; margin: 20px; }\n")
    html.append("        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }\n")
    html.append("        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n")
    html.append("        th { background-color: #f2f2f2; }\n")
    html.append("        h1, h2, h3 { color: #333; }\n")
    html.append("        .chart-container { margin-bottom: 30px; }\n")
    html.append("    </style>\n")
    html.append("</head>\n")
    html.append("<body>\n")

    html.append("    <h1>Head Acceleration X通道(001)异常检测综合报告</h1>\n")

    # 添加摘要表格
    html.append("    <h2>异常检测摘要</h2>\n")
    html.append("    <table>\n")
    html.append("        <tr>\n")
    html.append("            <th>数据集ID</th>\n")
    html.append("            <th>模型类型</th>\n")
    html.append("            <th>异常序列数</th>\n")
    html.append("            <th>总序列数</th>\n")
    html.append("            <th>异常百分比</th>\n")
    html.append("        </tr>\n")

    for dataset_id in sorted(results.keys()):
        for model_name in available_models:
            if model_name in results[dataset_id]:
                result = results[dataset_id][model_name]
                html.append("        <tr>\n")
                html.append(f"            <td>{dataset_id}</td>\n")
                html.append(f"            <td>{model_name}</td>\n")
                html.append(f"            <td>{result['anomaly_count']}</td>\n")
                html.append(f"            <td>{result['total_sequences']}</td>\n")
                html.append(f"            <td>{result['anomaly_percentage']:.2f}%</td>\n")
                html.append("        </tr>\n")

    html.append("    </table>\n")

    # 添加模型比较
    html.append("    <h2>模型性能比较</h2>\n")

    # 计算每个模型的平均异常检测率
    model_avg_rates = {}
    for model_name in available_models:
        rates = []
        for dataset_id in results:
            if model_name in results[dataset_id]:
                rates.append(results[dataset_id][model_name]['anomaly_percentage'])
        if rates:
            model_avg_rates[model_name] = sum(rates) / len(rates)
        else:
            model_avg_rates[model_name] = 0

    html.append("    <table>\n")
    html.append("        <tr>\n")
    html.append("            <th>模型类型</th>\n")
    html.append("            <th>平均异常检测率</th>\n")
    html.append("        </tr>\n")

    for model_name, avg_rate in model_avg_rates.items():
        html.append("        <tr>\n")
        html.append(f"            <td>{model_name}</td>\n")
        html.append(f"            <td>{avg_rate:.2f}%</td>\n")
        html.append("        </tr>\n")

    html.append("    </table>\n")

    # 添加结论和建议
    html.append("    <h2>结论与建议</h2>\n")
    html.append("    <p>根据上述分析，我们得出以下结论：</p>\n")
    html.append("    <ol>\n")

    # 找出性能最好的模型
    if model_avg_rates:
        best_model = max(model_avg_rates.items(), key=lambda x: x[1])[0]
        html.append(f"        <li>{best_model}模型在大多数数据集上表现最佳，平均异常检测率为{model_avg_rates[best_model]:.2f}%</li>\n")

        # 如果有孤立森林模型，添加与其他模型的比较
        if 'isolation_forest' in model_avg_rates:
            isolation_forest_rate = model_avg_rates['isolation_forest']
            other_models = {model: rate for model, rate in model_avg_rates.items() if model != 'isolation_forest'}
            if other_models:
                best_other_model = max(other_models.items(), key=lambda x: x[1])[0]
                best_other_model_rate = other_models[best_other_model]

                if isolation_forest_rate > best_other_model_rate:
                    html.append(f"        <li>孤立森林模型表现优于深度学习模型，平均异常检测率为{isolation_forest_rate:.2f}%，而{best_other_model}模型为{best_other_model_rate:.2f}%</li>\n")
                else:
                    html.append(f"        <li>深度学习模型({best_other_model})表现优于传统机器学习模型(孤立森林)，平均异常检测率为{best_other_model_rate:.2f}%，而孤立森林为{isolation_forest_rate:.2f}%</li>\n")

    html.append("        <li>不同数据集的异常特征有所不同，需要针对特定数据集选择合适的模型</li>\n")
    html.append("        <li>建议在实际应用中，结合多个模型的检测结果，提高异常检测的准确性</li>\n")
    html.append("    </ol>\n")

    html.append("</body>\n")
    html.append("</html>\n")

    # 保存HTML报告
    html_report_file = os.path.join(html_report_dir, 'anomaly_detection_report.html')
    with open(html_report_file, 'w') as f:
        f.writelines(html)

    print(f"HTML报告已保存到: {html_report_file}")

    # 更新网页报告
    update_web_report(results, available_models)

def update_web_report(results, available_models):
    """
    更新anomaly_detection.html网页报告

    参数:
    results: 异常检测结果字典
    available_models: 可用的模型列表
    """
    try:
        # 读取原始网页文件
        web_report_file = 'POC_reports_html/anomaly_detection.html'

        if not os.path.exists(web_report_file):
            print(f"错误: 网页报告文件 {web_report_file} 不存在")
            return

        with open(web_report_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 计算每个模型的平均异常检测率
        model_avg_rates = {}
        for model_name in available_models:
            rates = []
            for dataset_id in results:
                if model_name in results[dataset_id]:
                    rates.append(results[dataset_id][model_name]['anomaly_percentage'])
            if rates:
                model_avg_rates[model_name] = sum(rates) / len(rates)
            else:
                model_avg_rates[model_name] = 0

        # 找出性能最好的模型
        if model_avg_rates:
            best_model = max(model_avg_rates.items(), key=lambda x: x[1])[0]
        else:
            print("警告: 没有可用的模型性能数据")
            return

        # 更新表格数据
        # 查找表格部分
        table_start = content.find('<table class="min-w-full">')
        table_end = content.find('</table>', table_start)

        if table_start != -1 and table_end != -1:
            # 提取表格内容
            table_content = content[table_start:table_end + 8]

            # 创建新的表格内容
            new_table_content = '<table class="min-w-full">\n'
            new_table_content += '                            <thead>\n'
            new_table_content += '                                <tr>\n'
            new_table_content += '                                    <th>测试ID</th>\n'
            new_table_content += '                                    <th>异常序列数</th>\n'
            new_table_content += '                                    <th>总序列数</th>\n'
            new_table_content += '                                    <th>异常百分比</th>\n'
            new_table_content += '                                    <th>主要异常类型</th>\n'
            new_table_content += '                                    <th>置信度</th>\n'
            new_table_content += '                                </tr>\n'
            new_table_content += '                            </thead>\n'
            new_table_content += '                            <tbody>\n'

            # 添加每个数据集的结果
            for dataset_id in sorted(results.keys()):
                if best_model in results[dataset_id]:
                    result = results[dataset_id][best_model]
                    anomaly_count = result['anomaly_count']
                    total_sequences = result['total_sequences']
                    anomaly_percentage = result['anomaly_percentage']

                    # 根据异常百分比确定主要异常类型和置信度
                    if anomaly_percentage > 8:
                        anomaly_type = '断点异常'
                        confidence = '高'
                    elif anomaly_percentage > 5:
                        anomaly_type = '尖峰异常'
                        confidence = '高'
                    else:
                        anomaly_type = '波动异常'
                        confidence = '中'

                    new_table_content += '                                <tr>\n'
                    new_table_content += f'                                    <td>{dataset_id}.001</td>\n'
                    new_table_content += f'                                    <td>{anomaly_count}</td>\n'
                    new_table_content += f'                                    <td>{total_sequences}</td>\n'
                    new_table_content += f'                                    <td>{anomaly_percentage:.2f}%</td>\n'
                    new_table_content += f'                                    <td>{anomaly_type}</td>\n'
                    new_table_content += f'                                    <td>{confidence}</td>\n'
                    new_table_content += '                                </tr>\n'

            new_table_content += '                            </tbody>\n'
            new_table_content += '                        </table>'

            # 替换原表格内容
            content = content.replace(table_content, new_table_content)

            # 更新图表数据
            # 更新异常统计图表
            chart_data_start = content.find('const anomalyStatsOption = {')
            chart_data_end = content.find('};', chart_data_start)

            if chart_data_start != -1 and chart_data_end != -1:
                chart_data = content[chart_data_start:chart_data_end + 2]

                # 提取数据集ID和异常百分比
                dataset_ids = []
                anomaly_percentages = []

                for dataset_id in sorted(results.keys()):
                    if best_model in results[dataset_id]:
                        dataset_ids.append(f'{dataset_id}.001')
                        anomaly_percentages.append(results[dataset_id][best_model]['anomaly_percentage'])

                # 创建新的图表数据
                new_chart_data = 'const anomalyStatsOption = {\n'
                new_chart_data += '            title: {\n'
                new_chart_data += '                text: \'各测试组异常百分比\',\n'
                new_chart_data += '                left: \'center\'\n'
                new_chart_data += '            },\n'
                new_chart_data += '            tooltip: {\n'
                new_chart_data += '                trigger: \'axis\',\n'
                new_chart_data += '                axisPointer: {\n'
                new_chart_data += '                    type: \'shadow\'\n'
                new_chart_data += '                }\n'
                new_chart_data += '            },\n'
                new_chart_data += '            grid: {\n'
                new_chart_data += '                left: \'3%\',\n'
                new_chart_data += '                right: \'4%\',\n'
                new_chart_data += '                bottom: \'15%\',\n'
                new_chart_data += '                containLabel: true\n'
                new_chart_data += '            },\n'
                new_chart_data += '            xAxis: {\n'
                new_chart_data += '                type: \'category\',\n'
                new_chart_data += f'                data: {str(dataset_ids)},\n'
                new_chart_data += '                axisLabel: {\n'
                new_chart_data += '                    interval: 0,\n'
                new_chart_data += '                    rotate: 30\n'
                new_chart_data += '                }\n'
                new_chart_data += '            },\n'
                new_chart_data += '            yAxis: {\n'
                new_chart_data += '                type: \'value\',\n'
                new_chart_data += '                name: \'异常百分比 (%)\'\n'
                new_chart_data += '            },\n'
                new_chart_data += '            series: [\n'
                new_chart_data += '                {\n'
                new_chart_data += '                    type: \'bar\',\n'
                new_chart_data += f'                    data: {str(anomaly_percentages)},\n'
                new_chart_data += '                    itemStyle: {\n'
                new_chart_data += '                        color: function(params) {\n'
                new_chart_data += '                            const value = params.value;\n'
                new_chart_data += '                            if (value > 8) return \'#ef4444\';\n'
                new_chart_data += '                            if (value > 5) return \'#f59e0b\';\n'
                new_chart_data += '                            return \'#3b82f6\';\n'
                new_chart_data += '                        }\n'
                new_chart_data += '                    }\n'
                new_chart_data += '                }\n'
                new_chart_data += '            ]\n'
                new_chart_data += '        }'

                # 替换原图表数据
                content = content.replace(chart_data, new_chart_data)

        # 保存更新后的网页文件
        with open(web_report_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"网页报告已更新: {web_report_file}")

    except Exception as e:
        print(f"更新网页报告时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
