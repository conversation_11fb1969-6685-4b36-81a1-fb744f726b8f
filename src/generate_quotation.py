# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-30
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
技术服务报价单生成器脚本
为天检中心碰撞测试数据异常AI智能检测系统项目生成详细的技术服务报价单。
包含功能模块分解、工时计算、成本分析和Excel格式报价单输出。
基于ROI分析结果制定合理的人员配置和项目预算。
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime
import os

class QuotationGenerator:
    """报价单生成器"""

    def __init__(self):
        self.ai_engineer_rate = 30000  # AI工程师月薪（元）
        self.software_engineer_rate = 20000  # 软件工程师月薪（元）
        self.project_manager_rate = 22000  # 项目经理月薪（元）
        self.qa_engineer_rate = 18000  # 质量保证工程师月薪（元）
        self.working_days_per_month = 21.75  # 每月工作日

        # 基于ROI分析的目标成本（去除力学工程师）
        self.target_ai_months = 6  # AI工程师6个月
        self.target_software_months = 6.5  # 软件工程师6.5个月
        self.target_pm_months = 6.5  # 项目经理6.5个月
        self.target_qa_months = 4  # 质量保证4个月

        # 项目基本信息
        self.project_info = {
            'project_name': '天检中心-碰撞测试数据异常AI智能检测系统',
            'client_name': '中汽数据（天津）有限公司',
            'service_provider': '山东山创网络科技有限公司',
            'contact_person': '刁国亮',
            'contact_email': '<EMAIL>',
            'quote_date': datetime.now().strftime('%Y年%m月%d日'),
            'valid_period': '30天'
        }

        # 功能模块定义（精确匹配ROI分析：AI 6月=130天，软件 6.5月=141天，PM 6.5月=141天，QA 4月=87天）
        self.modules = [
            {
                'module_name': '数据预处理模块',
                'description': '数据清洗、标准化、缺失值处理、特征工程',
                'ai_days': 11,
                'software_days': 12,
                'pm_days': 12,
                'qa_days': 7,
                'deliverables': '数据预处理组件、数据质量报告'
            },
            {
                'module_name': '孤立森林异常检测模块',
                'description': '基于孤立森林算法的异常检测基线模型',
                'ai_days': 10,
                'software_days': 11,
                'pm_days': 11,
                'qa_days': 7,
                'deliverables': '孤立森林模型、检测接口、性能评估报告'
            },
            {
                'module_name': '简单自编码器模块',
                'description': '基于全连接神经网络的自编码器异常检测',
                'ai_days': 11,
                'software_days': 12,
                'pm_days': 12,
                'qa_days': 7,
                'deliverables': '简单自编码器模型、训练脚本、评估工具'
            },
            {
                'module_name': 'LSTM自编码器模块',
                'description': '基于长短期记忆网络的时序异常检测',
                'ai_days': 12,
                'software_days': 13,
                'pm_days': 13,
                'qa_days': 8,
                'deliverables': 'LSTM自编码器模型、时序处理组件'
            },
            {
                'module_name': '高级LSTM自编码器模块',
                'description': '具有注意力机制和残差连接的高级LSTM模型',
                'ai_days': 13,
                'software_days': 14,
                'pm_days': 14,
                'qa_days': 9,
                'deliverables': '高级LSTM模型、优化算法、性能基准'
            },
            {
                'module_name': '模型训练与优化模块',
                'description': '模型训练流程、超参数优化、模型选择',
                'ai_days': 12,
                'software_days': 13,
                'pm_days': 13,
                'qa_days': 8,
                'deliverables': '训练框架、优化工具、模型库'
            },
            {
                'module_name': '异常检测引擎',
                'description': '集成多模型的异常检测核心引擎',
                'ai_days': 11,
                'software_days': 12,
                'pm_days': 12,
                'qa_days': 8,
                'deliverables': '检测引擎、API接口、配置管理'
            },
            {
                'module_name': '数据可视化模块',
                'description': '异常检测结果可视化、报表生成',
                'ai_days': 10,
                'software_days': 13,
                'pm_days': 12,
                'qa_days': 7,
                'deliverables': '可视化组件、图表库、报表模板'
            },
            {
                'module_name': 'Web管理界面',
                'description': '系统管理界面、用户权限、操作日志',
                'ai_days': 9,
                'software_days': 14,
                'pm_days': 13,
                'qa_days': 8,
                'deliverables': 'Web界面、用户管理、系统监控'
            },
            {
                'module_name': '系统集成与部署',
                'description': '系统集成、部署配置、性能优化',
                'ai_days': 10,
                'software_days': 13,
                'pm_days': 14,
                'qa_days': 9,
                'deliverables': '部署方案、运维文档、性能报告'
            },
            {
                'module_name': '测试与验证',
                'description': '功能测试、性能测试、用户验收测试',
                'ai_days': 9,
                'software_days': 12,
                'pm_days': 13,
                'qa_days': 10,
                'deliverables': '测试报告、验收文档、质量保证'
            },
            {
                'module_name': '文档与培训',
                'description': '技术文档、用户手册、操作培训',
                'ai_days': 12,
                'software_days': 12,
                'pm_days': 12,
                'qa_days': 11,
                'deliverables': '技术文档、用户手册、培训材料'
            }
        ]

    def calculate_module_cost(self, module):
        """计算单个模块成本"""
        ai_cost = (module['ai_days'] / self.working_days_per_month) * self.ai_engineer_rate
        software_cost = (module['software_days'] / self.working_days_per_month) * self.software_engineer_rate
        pm_cost = (module['pm_days'] / self.working_days_per_month) * self.project_manager_rate
        qa_cost = (module['qa_days'] / self.working_days_per_month) * self.qa_engineer_rate

        total_cost = ai_cost + software_cost + pm_cost + qa_cost
        total_days = module['ai_days'] + module['software_days'] + module['pm_days'] + module['qa_days']

        return {
            'ai_cost': ai_cost,
            'software_cost': software_cost,
            'pm_cost': pm_cost,
            'qa_cost': qa_cost,
            'total_cost': total_cost,
            'total_days': total_days
        }

    def generate_quotation_sheet(self, workbook):
        """生成报价单主页"""
        ws = workbook.create_sheet("报价单", 0)

        # 设置列宽
        ws.column_dimensions['A'].width = 3
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 40
        ws.column_dimensions['D'].width = 15
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 15
        ws.column_dimensions['G'].width = 3

        # 标题样式
        title_font = Font(name='微软雅黑', size=18, bold=True, color='1F4E79')
        header_font = Font(name='微软雅黑', size=12, bold=True, color='1F4E79')
        normal_font = Font(name='微软雅黑', size=11)

        # 标题
        ws.merge_cells('B2:F2')
        ws['B2'] = '技术服务报价单'
        ws['B2'].font = title_font
        ws['B2'].alignment = Alignment(horizontal='center', vertical='center')

        # 项目信息
        row = 4
        ws[f'B{row}'] = '项目名称：'
        ws[f'B{row}'].font = header_font
        ws.merge_cells(f'C{row}:F{row}')
        ws[f'C{row}'] = self.project_info['project_name']
        ws[f'C{row}'].font = normal_font

        row += 1
        ws[f'B{row}'] = '客户单位：'
        ws[f'B{row}'].font = header_font
        ws[f'C{row}'] = self.project_info['client_name']
        ws[f'C{row}'].font = normal_font

        row += 1
        ws[f'B{row}'] = '服务商：'
        ws[f'B{row}'].font = header_font
        ws[f'C{row}'] = self.project_info['service_provider']
        ws[f'C{row}'].font = normal_font

        row += 1
        ws[f'B{row}'] = '报价日期：'
        ws[f'B{row}'].font = header_font
        ws[f'C{row}'] = self.project_info['quote_date']
        ws[f'C{row}'].font = normal_font

        ws[f'D{row}'] = '有效期：'
        ws[f'D{row}'].font = header_font
        ws[f'E{row}'] = self.project_info['valid_period']
        ws[f'E{row}'].font = normal_font

        # 报价汇总表
        row += 3
        ws.merge_cells(f'B{row}:F{row}')
        ws[f'B{row}'] = '报价汇总'
        ws[f'B{row}'].font = header_font
        ws[f'B{row}'].alignment = Alignment(horizontal='center')

        # 表头
        row += 1
        headers = ['序号', '服务内容', '人员配置', '工期(天)', '费用(元)']
        for i, header in enumerate(headers):
            cell = ws.cell(row=row, column=i+2, value=header)
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')

        # 计算总成本
        total_cost = 0
        total_days = 0

        for i, module in enumerate(self.modules):
            cost_info = self.calculate_module_cost(module)
            total_cost += cost_info['total_cost']
            total_days += cost_info['total_days']

            row += 1
            ws.cell(row=row, column=2, value=i+1).font = normal_font
            ws.cell(row=row, column=3, value=module['module_name']).font = normal_font

            # 人员配置
            personnel = f"AI工程师{module['ai_days']}天, 软件工程师{module['software_days']}天, 项目经理{module['pm_days']}天, 质量保证{module['qa_days']}天"
            ws.cell(row=row, column=4, value=personnel).font = normal_font

            ws.cell(row=row, column=5, value=cost_info['total_days']).font = normal_font
            ws.cell(row=row, column=6, value=f"{cost_info['total_cost']:,.0f}").font = normal_font

        # 总计行
        row += 1
        ws.cell(row=row, column=2, value='总计').font = header_font
        ws.cell(row=row, column=3, value='系统开发定制服务').font = header_font
        ws.cell(row=row, column=4, value='详见功能清单').font = header_font
        ws.cell(row=row, column=5, value=total_days).font = header_font
        ws.cell(row=row, column=6, value=f"{total_cost:,.0f}").font = header_font

        # 添加边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row_num in range(row-len(self.modules), row+1):
            for col_num in range(2, 7):
                ws.cell(row=row_num, column=col_num).border = thin_border

        # 服务说明
        row += 3
        ws[f'B{row}'] = '服务说明：'
        ws[f'B{row}'].font = header_font

        services = [
            '1. 提供完整的AI异常检测系统开发服务',
            '2. 包含四种异常检测算法的实现与优化',
            '3. 提供Web管理界面和数据可视化功能',
            '4. 包含系统部署、测试和技术文档',
            '5. 提供现场培训和技术支持服务',
            '6. 保证期内免费维护和bug修复'
        ]

        for service in services:
            row += 1
            ws[f'B{row}'] = service
            ws[f'B{row}'].font = normal_font

        # 联系信息
        row += 3
        ws[f'B{row}'] = '联系人：'
        ws[f'B{row}'].font = header_font
        ws[f'C{row}'] = self.project_info['contact_person']
        ws[f'C{row}'].font = normal_font

        row += 1
        ws[f'B{row}'] = '邮箱：'
        ws[f'B{row}'].font = header_font
        ws[f'C{row}'] = self.project_info['contact_email']
        ws[f'C{row}'].font = normal_font

        return total_cost, total_days

    def generate_detail_sheet(self, workbook):
        """生成功能列表工时清单"""
        ws = workbook.create_sheet("功能列表工时清单", 1)

        # 设置列宽
        ws.column_dimensions['A'].width = 3
        ws.column_dimensions['B'].width = 5
        ws.column_dimensions['C'].width = 25
        ws.column_dimensions['D'].width = 35
        ws.column_dimensions['E'].width = 12
        ws.column_dimensions['F'].width = 12
        ws.column_dimensions['G'].width = 12
        ws.column_dimensions['H'].width = 12
        ws.column_dimensions['I'].width = 15
        ws.column_dimensions['J'].width = 30
        ws.column_dimensions['K'].width = 3

        # 样式定义
        title_font = Font(name='微软雅黑', size=16, bold=True, color='1F4E79')
        header_font = Font(name='微软雅黑', size=11, bold=True, color='1F4E79')
        normal_font = Font(name='微软雅黑', size=10)

        # 标题
        ws.merge_cells('B2:J2')
        ws['B2'] = '功能列表工时清单'
        ws['B2'].font = title_font
        ws['B2'].alignment = Alignment(horizontal='center', vertical='center')

        # 人员单价说明
        row = 4
        ws.merge_cells(f'B{row}:K{row}')
        ws[f'B{row}'] = f'人员单价：AI工程师 {self.ai_engineer_rate:,}元/月，软件工程师 {self.software_engineer_rate:,}元/月，项目经理 {self.project_manager_rate:,}元/月，质量保证 {self.qa_engineer_rate:,}元/月（按{self.working_days_per_month}工作日/月计算）'
        ws[f'B{row}'].font = normal_font
        ws[f'B{row}'].alignment = Alignment(horizontal='center')

        # 表头
        row += 2
        headers = ['序号', '功能模块', '功能描述', 'AI工程师(天)', '软件工程师(天)', '项目经理(天)', '质量保证(天)', '总工时(天)', '总费用(元)', '交付物']
        for i, header in enumerate(headers):
            cell = ws.cell(row=row, column=i+2, value=header)
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')

        # 设置表头行高
        ws.row_dimensions[row].height = 30

        # 填充数据
        total_ai_days = 0
        total_software_days = 0
        total_pm_days = 0
        total_qa_days = 0
        total_cost = 0

        for i, module in enumerate(self.modules):
            cost_info = self.calculate_module_cost(module)

            total_ai_days += module['ai_days']
            total_software_days += module['software_days']
            total_pm_days += module['pm_days']
            total_qa_days += module['qa_days']
            total_cost += cost_info['total_cost']

            row += 1
            ws.cell(row=row, column=2, value=i+1).font = normal_font
            ws.cell(row=row, column=3, value=module['module_name']).font = normal_font
            ws.cell(row=row, column=4, value=module['description']).font = normal_font
            ws.cell(row=row, column=5, value=module['ai_days']).font = normal_font
            ws.cell(row=row, column=6, value=module['software_days']).font = normal_font
            ws.cell(row=row, column=7, value=module['pm_days']).font = normal_font
            ws.cell(row=row, column=8, value=module['qa_days']).font = normal_font
            ws.cell(row=row, column=9, value=cost_info['total_days']).font = normal_font
            ws.cell(row=row, column=10, value=f"{cost_info['total_cost']:,.0f}").font = normal_font
            ws.cell(row=row, column=11, value=module['deliverables']).font = normal_font

            # 设置行高和自动换行
            ws.row_dimensions[row].height = 40
            for col in range(2, 12):
                ws.cell(row=row, column=col).alignment = Alignment(vertical='center', wrap_text=True)

        # 总计行
        row += 1
        ws.cell(row=row, column=2, value='总计').font = header_font
        ws.cell(row=row, column=3, value='系统开发定制').font = header_font
        ws.cell(row=row, column=4, value='完整AI异常检测系统').font = header_font
        ws.cell(row=row, column=5, value=total_ai_days).font = header_font
        ws.cell(row=row, column=6, value=total_software_days).font = header_font
        ws.cell(row=row, column=7, value=total_pm_days).font = header_font
        ws.cell(row=row, column=8, value=total_qa_days).font = header_font
        ws.cell(row=row, column=9, value=total_ai_days + total_software_days + total_pm_days + total_qa_days).font = header_font
        ws.cell(row=row, column=10, value=f"{total_cost:,.0f}").font = header_font
        ws.cell(row=row, column=11, value='完整系统交付').font = header_font

        # 添加边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row_num in range(row-len(self.modules), row+1):
            for col_num in range(2, 12):
                ws.cell(row=row_num, column=col_num).border = thin_border

        # 成本分析
        row += 3
        ws[f'B{row}'] = '成本分析：'
        ws[f'B{row}'].font = header_font

        ai_cost = (total_ai_days / self.working_days_per_month) * self.ai_engineer_rate
        software_cost = (total_software_days / self.working_days_per_month) * self.software_engineer_rate
        pm_cost = (total_pm_days / self.working_days_per_month) * self.project_manager_rate
        qa_cost = (total_qa_days / self.working_days_per_month) * self.qa_engineer_rate

        cost_breakdown = [
            f'AI工程师成本：{total_ai_days}天 ÷ {self.working_days_per_month}天/月 × {self.ai_engineer_rate:,}元/月 = {ai_cost:,.0f}元',
            f'软件工程师成本：{total_software_days}天 ÷ {self.working_days_per_month}天/月 × {self.software_engineer_rate:,}元/月 = {software_cost:,.0f}元',
            f'项目经理成本：{total_pm_days}天 ÷ {self.working_days_per_month}天/月 × {self.project_manager_rate:,}元/月 = {pm_cost:,.0f}元',
            f'质量保证成本：{total_qa_days}天 ÷ {self.working_days_per_month}天/月 × {self.qa_engineer_rate:,}元/月 = {qa_cost:,.0f}元',
            f'总计：{total_cost:,.0f}元'
        ]

        for breakdown in cost_breakdown:
            row += 1
            ws[f'B{row}'] = breakdown
            ws[f'B{row}'].font = normal_font

        return total_cost

    def generate_quotation(self, output_path):
        """生成完整报价单"""
        # 创建工作簿
        workbook = openpyxl.Workbook()

        # 删除默认工作表
        workbook.remove(workbook.active)

        # 生成报价单主页
        total_cost, total_days = self.generate_quotation_sheet(workbook)

        # 生成功能清单
        detail_cost = self.generate_detail_sheet(workbook)

        # 保存文件
        workbook.save(output_path)

        print(f"报价单已生成：{output_path}")
        print(f"项目总成本：{total_cost:,.0f}元")
        print(f"项目总工期：{total_days}天")
        print(f"预计项目周期：{total_days/self.working_days_per_month:.1f}个月")

        return total_cost, total_days

def main():
    """主函数"""
    # 创建报价单生成器
    generator = QuotationGenerator()

    # 生成报价单
    output_path = "docs/天检中心-碰撞测试数据异常AI智能检测系统-技术服务报价单-山东山创.xlsx"

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    total_cost, total_days = generator.generate_quotation(output_path)

    print("\n报价单生成完成！")
    print("="*50)
    print(f"文件位置：{output_path}")
    print(f"项目名称：{generator.project_info['project_name']}")
    print(f"服务商：{generator.project_info['service_provider']}")
    print(f"总报价：{total_cost:,.0f}元")
    print(f"项目工期：{total_days}天（约{total_days/generator.working_days_per_month:.1f}个月）")

if __name__ == "__main__":
    main()
