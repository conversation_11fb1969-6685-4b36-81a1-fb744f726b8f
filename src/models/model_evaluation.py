# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

# 模型评估可视化工具脚本
# 提供全面的模型评估和可视化功能，包括训练历史、混淆矩阵、PR曲线、ROC曲线、
# 梯度直方图、特征图可视化、Grad-CAM热力图、重构误差分布、潜空间可视化等。
# 支持TensorFlow/Keras模型的深度分析和异常检测结果可视化。

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, precision_recall_curve, average_precision_score, roc_curve, auc
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow.keras.models import Model
import joblib
from tqdm import tqdm

# 设置目录
results_dir = 'results'
os.makedirs(results_dir, exist_ok=True)

# 绘制训练历史
def plot_training_history(history, model_name):
    """
    绘制模型训练历史，包括损失函数和准确率曲线

    参数:
    history: 训练历史对象
    model_name: 模型名称
    """
    try:
        # 检查历史对象是否有效
        if history is None:
            print(f"警告: 历史对象为None，无法绘制训练历史")

            # 创建一个空白图像，显示错误信息
            plt.figure(figsize=(15, 6))
            plt.text(0.5, 0.5, "训练历史对象为None",
                    horizontalalignment='center', verticalalignment='center',
                    fontsize=16, color='red')
            plt.axis('off')
            plt.savefig(os.path.join(results_dir, f'{model_name}_training_history.png'))
            plt.close()
            return

        if not hasattr(history, 'history'):
            print(f"警告: 历史对象没有history属性，尝试创建一个空的history")
            # 尝试创建一个空的history属性
            history.history = {'loss': [], 'val_loss': []}

        if not history.history:
            print(f"警告: 历史对象的history为空，无法绘制训练历史")

            # 创建一个空白图像，显示错误信息
            plt.figure(figsize=(15, 6))
            plt.text(0.5, 0.5, "训练历史数据为空",
                    horizontalalignment='center', verticalalignment='center',
                    fontsize=16, color='red')
            plt.axis('off')
            plt.savefig(os.path.join(results_dir, f'{model_name}_training_history.png'))
            plt.close()
            return

        # 打印历史数据的键，用于调试
        print(f"历史数据包含以下键: {list(history.history.keys())}")
        print(f"损失函数值: {history.history.get('loss', [])}")

        # 创建图像
        plt.figure(figsize=(15, 6))

        # 检查是否有足够的数据点
        if len(history.history.get('loss', [])) == 0:
            print(f"警告: 损失函数数据为空，无法绘制训练历史")
            plt.text(0.5, 0.5, "训练历史数据为空",
                    horizontalalignment='center', verticalalignment='center',
                    fontsize=16, color='red')
            plt.axis('off')
        else:
            # 绘制损失函数曲线
            plt.subplot(1, 2, 1)
            plt.plot(history.history['loss'], label='Training Loss')
            if 'val_loss' in history.history:
                plt.plot(history.history['val_loss'], label='Validation Loss')
            plt.title(f'{model_name} - Loss Curve')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True, alpha=0.3)

            # 绘制准确率曲线（如果有）
            if 'accuracy' in history.history or 'acc' in history.history:
                plt.subplot(1, 2, 2)
                if 'accuracy' in history.history:
                    plt.plot(history.history['accuracy'], label='Training Accuracy')
                elif 'acc' in history.history:
                    plt.plot(history.history['acc'], label='Training Accuracy')

                if 'val_accuracy' in history.history:
                    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
                elif 'val_acc' in history.history:
                    plt.plot(history.history['val_acc'], label='Validation Accuracy')

                plt.title(f'{model_name} - Accuracy Curve')
                plt.xlabel('Epoch')
                plt.ylabel('Accuracy')
                plt.legend()
                plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(results_dir, f'{model_name}_training_history.png'))
        plt.close()
        print(f"训练历史图已保存到: {os.path.join(results_dir, f'{model_name}_training_history.png')}")

    except Exception as e:
        print(f"绘制训练历史时出错: {str(e)}")

        # 创建一个空白图像，显示错误信息
        plt.figure(figsize=(15, 6))
        plt.text(0.5, 0.5, f"绘制训练历史时出错: {str(e)}",
                horizontalalignment='center', verticalalignment='center',
                fontsize=12, color='red')
        plt.axis('off')
        plt.savefig(os.path.join(results_dir, f'{model_name}_training_history.png'))
        plt.close()

# 绘制混淆矩阵
def plot_confusion_matrix(y_true, y_pred, model_name, class_names=None):
    """
    绘制混淆矩阵

    参数:
    y_true: 真实标签
    y_pred: 预测标签
    model_name: 模型名称
    class_names: 类别名称
    """
    cm = confusion_matrix(y_true, y_pred)

    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names if class_names else 'auto',
                yticklabels=class_names if class_names else 'auto')
    plt.title(f'{model_name} - Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, f'{model_name}_confusion_matrix.png'))
    plt.close()

# 绘制PR曲线
def plot_precision_recall_curve(y_true, y_score, model_name):
    """
    绘制精确率-召回率曲线

    参数:
    y_true: 真实标签
    y_score: 预测分数
    model_name: 模型名称
    """
    precision, recall, thresholds = precision_recall_curve(y_true, y_score)
    average_precision = average_precision_score(y_true, y_score)

    plt.figure(figsize=(10, 8))
    plt.plot(recall, precision, lw=2, label=f'AP = {average_precision:.3f}')
    plt.fill_between(recall, precision, alpha=0.2, color='b')
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.ylim([0.0, 1.05])
    plt.xlim([0.0, 1.0])
    plt.title(f'{model_name} - Precision-Recall Curve')
    plt.legend(loc="lower left")
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(results_dir, f'{model_name}_pr_curve.png'))
    plt.close()

# 绘制ROC曲线
def plot_roc_curve(y_true, y_score, model_name):
    """
    绘制ROC曲线

    参数:
    y_true: 真实标签
    y_score: 预测分数
    model_name: 模型名称
    """
    fpr, tpr, thresholds = roc_curve(y_true, y_score)
    roc_auc = auc(fpr, tpr)

    plt.figure(figsize=(10, 8))
    plt.plot(fpr, tpr, lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(f'{model_name} - ROC Curve')
    plt.legend(loc="lower right")
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(results_dir, f'{model_name}_roc_curve.png'))
    plt.close()

# 绘制梯度直方图（仅适用于TensorFlow模型）
def plot_gradient_histogram(model, X, y, model_name):
    """
    绘制梯度直方图

    参数:
    model: TensorFlow模型
    X: 输入数据
    y: 目标数据
    model_name: 模型名称
    """
    if not isinstance(model, tf.keras.Model):
        print("梯度直方图仅适用于TensorFlow模型")
        return

    # 创建梯度磁带
    with tf.GradientTape() as tape:
        # 前向传播
        predictions = model(X)
        if isinstance(predictions, tuple):
            predictions = predictions[0]

        # 计算损失
        if len(y.shape) == 1 or y.shape[1] == 1:  # 二分类或回归
            loss = tf.keras.losses.mean_squared_error(y, predictions)
        else:  # 多分类
            loss = tf.keras.losses.categorical_crossentropy(y, predictions)

    # 计算梯度
    gradients = tape.gradient(loss, model.trainable_variables)

    # 绘制梯度直方图
    plt.figure(figsize=(15, 10))
    for i, grad in enumerate(gradients):
        if i >= 9:  # 最多显示9个直方图
            break

        plt.subplot(3, 3, i+1)
        plt.hist(grad.numpy().flatten(), bins=50)
        plt.title(f'Layer {i+1}')
        plt.xlabel('Gradient Value')
        plt.ylabel('Count')

    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, f'{model_name}_gradient_histogram.png'))
    plt.close()

# 绘制特征图可视化（仅适用于CNN模型）
def plot_feature_maps(model, X, layer_names, model_name, n_samples=1):
    """
    绘制特征图可视化

    参数:
    model: TensorFlow模型
    X: 输入数据
    layer_names: 要可视化的层名称列表
    model_name: 模型名称
    n_samples: 要可视化的样本数量
    """
    if not isinstance(model, tf.keras.Model):
        print("特征图可视化仅适用于TensorFlow模型")
        return

    # 选择样本
    if n_samples > X.shape[0]:
        n_samples = X.shape[0]

    X_samples = X[:n_samples]

    # 为每个指定的层创建一个输出模型
    layer_outputs = [model.get_layer(name).output for name in layer_names]
    activation_model = Model(inputs=model.input, outputs=layer_outputs)

    # 获取特征图
    activations = activation_model.predict(X_samples)

    # 绘制特征图
    for i, layer_name in enumerate(layer_names):
        if i >= len(activations):
            continue

        activation = activations[i]

        # 对于每个样本
        for sample_idx in range(n_samples):
            plt.figure(figsize=(15, 10))

            # 获取当前样本的激活值
            sample_activation = activation[sample_idx]

            # 确定子图的行列数
            n_features = sample_activation.shape[-1]
            size = int(np.ceil(np.sqrt(n_features)))

            # 绘制每个特征图
            for j in range(min(64, n_features)):  # 最多显示64个特征图
                plt.subplot(size, size, j+1)

                # 对于2D特征图
                if len(sample_activation.shape) == 3:
                    plt.imshow(sample_activation[:, :, j], cmap='viridis')
                # 对于1D特征图
                else:
                    plt.plot(sample_activation[:, j])

                plt.axis('off')

            plt.suptitle(f'{model_name} - {layer_name} - Sample {sample_idx+1}')
            plt.tight_layout()
            plt.savefig(os.path.join(results_dir, f'{model_name}_{layer_name}_sample{sample_idx+1}_feature_maps.png'))
            plt.close()

# 绘制Grad-CAM热力图（仅适用于CNN模型）
def plot_grad_cam(model, X, y, layer_name, model_name, n_samples=1):
    """
    绘制Grad-CAM热力图

    参数:
    model: TensorFlow模型
    X: 输入数据
    y: 目标数据
    layer_name: 要可视化的层名称
    model_name: 模型名称
    n_samples: 要可视化的样本数量
    """
    if not isinstance(model, tf.keras.Model):
        print("Grad-CAM热力图仅适用于TensorFlow模型")
        return

    # 选择样本
    if n_samples > X.shape[0]:
        n_samples = X.shape[0]

    X_samples = X[:n_samples]
    y_samples = y[:n_samples]

    # 获取指定层的输出
    grad_model = Model(inputs=model.inputs, outputs=[model.get_layer(layer_name).output, model.output])

    # 对于每个样本
    for sample_idx in range(n_samples):
        x = X_samples[sample_idx:sample_idx+1]

        # 计算梯度
        with tf.GradientTape() as tape:
            conv_output, predictions = grad_model(x)
            if isinstance(predictions, tuple):
                predictions = predictions[0]

            if len(y_samples.shape) == 1:  # 二分类
                loss = predictions[:, 0]
            elif y_samples.shape[1] == 1:  # 二分类
                loss = predictions[:, 0]
            else:  # 多分类
                class_idx = np.argmax(y_samples[sample_idx])
                loss = predictions[:, class_idx]

        # 提取梯度
        grads = tape.gradient(loss, conv_output)

        # 计算导向反向传播
        pooled_grads = tf.reduce_mean(grads, axis=(0, 1, 2))

        # 加权求和
        conv_output = conv_output.numpy()[0]
        pooled_grads = pooled_grads.numpy()

        for i in range(pooled_grads.shape[-1]):
            conv_output[:, :, i] *= pooled_grads[i]

        # 计算热力图
        heatmap = np.mean(conv_output, axis=-1)
        heatmap = np.maximum(heatmap, 0)
        heatmap /= np.max(heatmap) if np.max(heatmap) > 0 else 1

        # 绘制热力图
        plt.figure(figsize=(10, 8))
        plt.matshow(heatmap, cmap='viridis')
        plt.title(f'{model_name} - Grad-CAM - Sample {sample_idx+1}')
        plt.colorbar()
        plt.savefig(os.path.join(results_dir, f'{model_name}_grad_cam_sample{sample_idx+1}.png'))
        plt.close()

# 绘制重构误差分布
def plot_reconstruction_error_distribution(train_errors, test_errors, threshold, model_name):
    """
    绘制重构误差分布

    参数:
    train_errors: 训练集重构误差
    test_errors: 测试集重构误差
    threshold: 异常检测阈值
    model_name: 模型名称
    """
    plt.figure(figsize=(12, 6))
    plt.hist(train_errors, bins=50, alpha=0.5, label='Training Data')
    plt.hist(test_errors, bins=50, alpha=0.5, label='Test Data')

    plt.axvline(x=threshold, color='r', linestyle='--', label=f'Threshold: {threshold:.4f}')

    plt.title(f'{model_name} - Reconstruction Error Distribution')
    plt.xlabel('Reconstruction Error')
    plt.ylabel('Count')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(results_dir, f'{model_name}_reconstruction_error_distribution.png'))
    plt.close()

# 绘制潜空间可视化
def plot_latent_space(encoder, X, model_name, n_samples=1000, perplexity=30):
    """
    使用t-SNE绘制潜空间可视化

    参数:
    encoder: 编码器模型
    X: 输入数据
    model_name: 模型名称
    n_samples: 要可视化的样本数量
    perplexity: t-SNE的perplexity参数
    """
    try:
        from sklearn.manifold import TSNE

        print(f"开始生成潜空间可视化，使用 {n_samples} 个样本...")

        # 选择样本
        if n_samples > X.shape[0]:
            n_samples = X.shape[0]
            print(f"样本数量调整为 {n_samples}")

        X_samples = X[:n_samples]

        # 获取潜空间表示
        print("计算潜空间表示...")
        latent_vectors = encoder.predict(X_samples)
        print(f"潜空间表示形状: {latent_vectors.shape}")

        # 使用t-SNE降维
        print(f"使用t-SNE降维，perplexity={perplexity}...")
        tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=1000, random_state=42)
        latent_tsne = tsne.fit_transform(latent_vectors)
        print(f"t-SNE降维完成，形状: {latent_tsne.shape}")

        # 绘制t-SNE结果
        plt.figure(figsize=(12, 10))
        scatter = plt.scatter(latent_tsne[:, 0], latent_tsne[:, 1],
                             c=np.arange(n_samples), cmap='viridis',
                             alpha=0.7, s=10)
        plt.colorbar(scatter, label='Sample Index')
        plt.title(f'{model_name} - Latent Space Visualization (t-SNE)')
        plt.xlabel('t-SNE Dimension 1')
        plt.ylabel('t-SNE Dimension 2')
        plt.grid(True, alpha=0.3)

        # 保存图像
        output_file = os.path.join(results_dir, f'{model_name}_latent_space_tsne.png')
        plt.savefig(output_file, dpi=300)
        plt.close()
        print(f"潜空间可视化已保存到: {output_file}")

        return True
    except Exception as e:
        print(f"绘制潜空间可视化时出错: {str(e)}")

        # 创建一个空白图像，显示错误信息
        plt.figure(figsize=(12, 10))
        plt.text(0.5, 0.5, f"绘制潜空间可视化时出错: {str(e)}",
                horizontalalignment='center', verticalalignment='center',
                fontsize=12, color='red')
        plt.axis('off')
        plt.savefig(os.path.join(results_dir, f'{model_name}_latent_space_tsne.png'))
        plt.close()

        return False

# 绘制异常检测结果
def plot_anomaly_detection_results(data, anomalies, model_name):
    """
    绘制异常检测结果

    参数:
    data: 原始数据
    anomalies: 异常检测结果
    model_name: 模型名称
    """
    plt.figure(figsize=(15, 8))

    # 绘制原始时间序列
    plt.plot(data['time'], data['value'], label='Original Data')

    # 标记异常区域
    anomaly_sequences = anomalies[anomalies['is_anomaly']]
    for _, row in anomaly_sequences.iterrows():
        start_idx = row['start_idx']
        end_idx = row['end_idx']
        plt.axvspan(data['time'].iloc[start_idx], data['time'].iloc[end_idx],
                   alpha=0.3, color='red')

    plt.title(f'{model_name} - Anomaly Detection Results')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(results_dir, f'{model_name}_anomaly_detection_results.png'))
    plt.close()
