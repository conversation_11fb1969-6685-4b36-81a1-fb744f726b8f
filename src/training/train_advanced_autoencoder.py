# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级自编码器模型训练脚本
训练用于时间序列异常检测的高级自编码器模型，包含多种先进技术：
1. 更深的网络结构（多层LSTM或全连接层）
2. 双向LSTM和注意力机制
3. 残差连接和批归一化
4. 高级激活函数和正则化技术
5. 支持LSTM和Dense两种架构选择
主要用于提升异常检测的准确性和鲁棒性。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, Dense, Dropout, BatchNormalization, LeakyReLU,
    Concatenate, Add, Multiply, Reshape, Permute, Lambda,
    LSTM, Bidirectional, TimeDistributed, RepeatVector,
    GlobalAveragePooling1D
)
from tensorflow.keras.callbacks import (
    EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
)
import joblib
from tqdm import tqdm
from datetime import datetime
import sys
sys.path.append('src/models')
try:
    from model_evaluation import (
        plot_training_history, plot_reconstruction_error_distribution,
        plot_precision_recall_curve, plot_roc_curve, plot_confusion_matrix,
        plot_latent_space
    )
except ImportError:
    print("无法导入model_evaluation模块，使用简单的绘图函数")

    # 定义简单的绘图函数
    def plot_training_history(history, model_name):
        """绘制训练历史"""
        plt.figure(figsize=(15, 6))
        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='Training Loss')
        if 'val_loss' in history.history:
            plt.plot(history.history['val_loss'], label='Validation Loss')
        plt.title(f'{model_name} - Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(results_dir, f'{model_name}_training_history.png'))
        plt.close()

    def plot_reconstruction_error_distribution(train_errors, test_errors, threshold, model_name):
        """绘制重构误差分布"""
        plt.figure(figsize=(12, 6))
        plt.hist(train_errors, bins=50, alpha=0.5, label='Training Data')
        plt.hist(test_errors, bins=50, alpha=0.5, label='Test Data')
        plt.axvline(x=threshold, color='r', linestyle='--', label=f'Threshold: {threshold:.4f}')
        plt.title(f'{model_name} - Reconstruction Error Distribution')
        plt.xlabel('Reconstruction Error')
        plt.ylabel('Count')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(results_dir, f'{model_name}_reconstruction_error_distribution.png'))
        plt.close()

    def plot_precision_recall_curve(y_true, y_score, model_name):
        """绘制精确率-召回率曲线"""
        from sklearn.metrics import precision_recall_curve, average_precision_score
        precision, recall, _ = precision_recall_curve(y_true, y_score)
        average_precision = average_precision_score(y_true, y_score)
        plt.figure(figsize=(10, 8))
        plt.plot(recall, precision, lw=2, label=f'AP = {average_precision:.3f}')
        plt.fill_between(recall, precision, alpha=0.2, color='b')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.ylim([0.0, 1.05])
        plt.xlim([0.0, 1.0])
        plt.title(f'{model_name} - Precision-Recall Curve')
        plt.legend(loc="lower left")
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(results_dir, f'{model_name}_pr_curve.png'))
        plt.close()

    def plot_roc_curve(y_true, y_score, model_name):
        """绘制ROC曲线"""
        from sklearn.metrics import roc_curve, auc
        fpr, tpr, _ = roc_curve(y_true, y_score)
        roc_auc = auc(fpr, tpr)
        plt.figure(figsize=(10, 8))
        plt.plot(fpr, tpr, lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', lw=2)
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'{model_name} - ROC Curve')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(results_dir, f'{model_name}_roc_curve.png'))
        plt.close()

    def plot_confusion_matrix(y_true, y_pred, model_name, class_names=None):
        """绘制混淆矩阵"""
        from sklearn.metrics import confusion_matrix
        import seaborn as sns
        cm = confusion_matrix(y_true, y_pred)
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=class_names if class_names else 'auto',
                    yticklabels=class_names if class_names else 'auto')
        plt.title(f'{model_name} - Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig(os.path.join(results_dir, f'{model_name}_confusion_matrix.png'))
        plt.close()

    def plot_latent_space(encoder, X, model_name, n_samples=1000, perplexity=30):
        """使用t-SNE绘制潜空间可视化"""
        try:
            from sklearn.manifold import TSNE
            # 选择样本
            if n_samples > X.shape[0]:
                n_samples = X.shape[0]
            X_samples = X[:n_samples]
            # 获取潜空间表示
            latent_vectors = encoder.predict(X_samples)
            # 使用t-SNE降维
            tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=1000, random_state=42)
            latent_tsne = tsne.fit_transform(latent_vectors)
            # 绘制t-SNE结果
            plt.figure(figsize=(12, 10))
            scatter = plt.scatter(latent_tsne[:, 0], latent_tsne[:, 1],
                                 c=np.arange(n_samples), cmap='viridis',
                                 alpha=0.7, s=10)
            plt.colorbar(scatter, label='Sample Index')
            plt.title(f'{model_name} - Latent Space Visualization (t-SNE)')
            plt.xlabel('t-SNE Dimension 1')
            plt.ylabel('t-SNE Dimension 2')
            plt.grid(True, alpha=0.3)
            plt.savefig(os.path.join(results_dir, f'{model_name}_latent_space_tsne.png'))
            plt.close()
            return True
        except Exception as e:
            print(f"绘制潜空间可视化时出错: {str(e)}")
            return False

# 设置随机种子以确保结果可重现
np.random.seed(42)
tf.random.set_seed(42)

# 设置目录
data_dir = 'data/processed'
model_dir = 'models'
results_dir = 'results'
os.makedirs(model_dir, exist_ok=True)
os.makedirs(results_dir, exist_ok=True)

# 定义参数
SEQUENCE_LENGTH = 100  # 序列长度
STRIDE = 20  # 滑动窗口步长
BATCH_SIZE = 64  # 批量大小 - 减小批量大小以提高稳定性
EPOCHS = 5  # 训练轮数 - 减少训练轮数以便快速测试
VALIDATION_SPLIT = 0.2
RANDOM_STATE = 42

# 数据预处理函数
def preprocess_data(df, test_ids=None, scaler=None):
    """
    预处理数据，将时间序列转换为适合自编码器的格式

    参数:
    df: 包含时间序列数据的DataFrame
    test_ids: 要包含的测试ID列表，如果为None则包含所有ID
    scaler: 用于标准化数据的scaler，如果为None则创建新的scaler

    返回:
    X: 预处理后的输入数据
    scaler: 用于标准化的scaler对象
    sequence_info: 序列信息（测试ID，开始索引等）
    """
    # 如果指定了测试ID，则只选择这些ID的数据
    if test_ids is not None:
        df = df[df['test_id'].isin(test_ids)]

    # 按测试ID和时间排序
    df = df.sort_values(['test_id', 'time'])

    # 标准化数据
    if scaler is None:
        scaler = StandardScaler()
        values_scaled = scaler.fit_transform(df[['value']])
    else:
        values_scaled = scaler.transform(df[['value']])

    df['value_scaled'] = values_scaled

    # 创建序列数据
    sequences = []
    sequence_info = []  # 存储序列的元信息（测试ID，开始索引等）

    # 按测试ID分组处理
    for test_id, group in tqdm(df.groupby('test_id'), desc="创建序列"):
        values = group['value_scaled'].values

        # 使用滑动窗口创建序列
        for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
            seq = values[i:i+SEQUENCE_LENGTH]
            sequences.append(seq)
            sequence_info.append({
                'test_id': test_id,
                'start_idx': i,
                'end_idx': i + SEQUENCE_LENGTH - 1
            })

    # 转换为numpy数组
    X = np.array(sequences)
    X = X.reshape(X.shape[0], X.shape[1], 1)  # 重塑为(样本数, 时间步, 特征数)

    return X, scaler, sequence_info

# 简化的自注意力层
def simple_attention_layer(inputs, name=None):
    """
    实现简化的自注意力机制

    参数:
    inputs: 输入张量，形状为(batch_size, time_steps, features)
    name: 层名称

    返回:
    context_vector: 上下文向量，形状为(batch_size, features)
    """
    # 使用全局平均池化作为简化的注意力机制
    # 这将把形状从(batch_size, time_steps, features)变为(batch_size, features)
    context_vector = GlobalAveragePooling1D(name=f'{name}_attention')(inputs)

    return context_vector

# 残差块
def residual_block(inputs, units, dropout_rate=0.2):
    """
    实现残差块

    参数:
    inputs: 输入张量
    units: 神经元数量
    dropout_rate: Dropout比率

    返回:
    outputs: 残差块的输出
    """
    # 主路径
    x = Dense(units)(inputs)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.1)(x)
    x = Dropout(dropout_rate)(x)

    x = Dense(int(inputs.shape[-1]))(x)
    x = BatchNormalization()(x)

    # 残差连接
    outputs = Add()([inputs, x])
    outputs = LeakyReLU(alpha=0.1)(outputs)

    return outputs

# 创建高级自编码器模型
def create_advanced_autoencoder(input_shape, model_type='lstm'):
    """
    创建高级自编码器模型

    参数:
    input_shape: 输入数据的形状 (时间步, 特征数)
    model_type: 模型类型，'lstm'或'dense'

    返回:
    model: 编译好的自编码器模型
    encoder: 编码器部分
    """
    # 输入层
    inputs = Input(shape=input_shape)

    # 编码器部分
    if model_type == 'lstm':
        # 第一层LSTM编码器
        x = Bidirectional(LSTM(64, return_sequences=True,
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(inputs)
        x = BatchNormalization()(x)

        # 第二层LSTM编码器
        x = Bidirectional(LSTM(32, return_sequences=True,
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(x)
        x = BatchNormalization()(x)

        # 第三层LSTM编码器
        x = Bidirectional(LSTM(16, return_sequences=False,  # 不返回序列
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(x)
        x = BatchNormalization()(x)

        # 全连接层
        x = Dense(32)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 潜在表示
        encoded = Dense(16, name='latent_space', activation='tanh')(x)

        # 解码器部分
        x = Dense(32)(encoded)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 重复向量以匹配序列长度
        x = RepeatVector(input_shape[0])(x)

        # 第一层LSTM解码器
        x = Bidirectional(LSTM(16, return_sequences=True,
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(x)
        x = BatchNormalization()(x)

        # 第二层LSTM解码器
        x = Bidirectional(LSTM(32, return_sequences=True,
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(x)
        x = BatchNormalization()(x)

        # 第三层LSTM解码器
        x = Bidirectional(LSTM(64, return_sequences=True,
                              kernel_regularizer=tf.keras.regularizers.l2(1e-4),
                              recurrent_dropout=0.1))(x)
        x = BatchNormalization()(x)

        # 输出层
        outputs = TimeDistributed(Dense(input_shape[1]))(x)

    else:  # 'dense'
        # 展平输入
        x = Reshape((input_shape[0] * input_shape[1],))(inputs)

        # 编码器层 - 更深的网络
        x = Dense(512)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 残差块1
        x = residual_block(x, 512, dropout_rate=0.2)

        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 残差块2
        x = residual_block(x, 256, dropout_rate=0.2)

        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        x = Dense(64)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 潜在表示 - 使用tanh激活函数
        encoded = Dense(32, name='latent_space', activation='tanh')(x)

        # 解码器层
        x = Dense(64)(encoded)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        x = Dense(128)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 残差块3
        x = residual_block(x, 128, dropout_rate=0.2)

        x = Dense(256)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 残差块4
        x = residual_block(x, 256, dropout_rate=0.2)

        x = Dense(512)(x)
        x = LeakyReLU(alpha=0.1)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        # 输出层
        outputs = Dense(input_shape[0] * input_shape[1], activation='linear')(x)
        outputs = Reshape(input_shape)(outputs)

    # 创建模型
    model = Model(inputs=inputs, outputs=outputs, name=f"advanced_{model_type}_autoencoder")

    # 创建编码器模型
    encoder = Model(inputs=inputs, outputs=encoded, name=f"advanced_{model_type}_encoder")

    # 使用带有梯度裁剪的优化器，降低学习率以提高稳定性
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.0005, clipnorm=1.0)

    # 编译模型，使用均方误差损失函数
    model.compile(optimizer=optimizer, loss='mse')

    print(f"高级{model_type}自编码器模型创建完成")

    return model, encoder

# 训练模型
def train_model(X_train, X_val, model_type='lstm'):
    """
    训练高级自编码器模型

    参数:
    X_train: 训练数据
    X_val: 验证数据
    model_type: 模型类型，'lstm'或'dense'

    返回:
    model: 训练好的模型
    encoder: 编码器模型
    history: 训练历史
    """
    input_shape = (X_train.shape[1], X_train.shape[2])
    model, encoder = create_advanced_autoencoder(input_shape, model_type)

    # 创建日志目录 - 使用更简单的名称避免路径问题
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = os.path.join('logs', f"advanced_{model_type}_{current_time}")

    # 确保logs目录存在
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 创建日志子目录
    try:
        os.makedirs(log_dir, exist_ok=True)
        print(f"TensorBoard日志目录创建成功: {log_dir}")
    except Exception as e:
        print(f"创建日志目录时出错: {str(e)}")
        # 使用备用目录
        log_dir = os.path.join('logs', f"advanced_{model_type}_backup")
        os.makedirs(log_dir, exist_ok=True)
        print(f"使用备用日志目录: {log_dir}")

    # 设置回调函数
    callbacks = [
        # 早停
        EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        ),
        # 模型检查点
        ModelCheckpoint(
            os.path.join(model_dir, f'advanced_{model_type}_autoencoder.h5'),
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),
        # 定期保存检查点
        ModelCheckpoint(
            os.path.join(model_dir, f'advanced_{model_type}_autoencoder_epoch_{{epoch:02d}}.h5'),
            save_freq=5*BATCH_SIZE,  # 每5个批次保存一次
            verbose=1
        ),
        # 学习率调度器
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-6,
            verbose=1
        ),
        # TensorBoard回调 - 更详细的配置
        tf.keras.callbacks.TensorBoard(
            log_dir=log_dir,
            histogram_freq=1,
            write_graph=True,  # 写入计算图
            write_images=False,  # 不写入图像以减少复杂性
            update_freq='epoch',
            profile_batch=0,
            embeddings_freq=1,  # 每个epoch保存嵌入
            embeddings_metadata=None  # 不使用元数据
        ),
        # 添加标量日志回调
        tf.keras.callbacks.CSVLogger(
            os.path.join(log_dir, 'training_log.csv'),
            separator=',',
            append=False
        )
    ]

    # 训练模型
    print(f"开始训练高级{model_type}自编码器模型，训练集形状: {X_train.shape}，验证集形状: {X_val.shape}")
    print(f"训练参数: epochs={EPOCHS}, batch_size={BATCH_SIZE}")

    try:
        # 保存模型摘要到文件
        model_summary_file = os.path.join(log_dir, 'model_summary.txt')
        with open(model_summary_file, 'w') as f:
            # 将模型摘要重定向到文件
            model.summary(print_fn=lambda x: f.write(x + '\n'))
        print(f"模型摘要已保存到: {model_summary_file}")

        # 打印模型摘要到控制台
        model.summary()

        # 创建自定义回调函数来保存历史数据
        class SaveHistoryCallback(tf.keras.callbacks.Callback):
            def __init__(self, filepath):
                super(SaveHistoryCallback, self).__init__()
                self.filepath = filepath
                self.history_dict = {'loss': [], 'val_loss': []}

            def on_epoch_end(self, epoch, logs=None):
                logs = logs or {}
                for key, value in logs.items():
                    if key not in self.history_dict:
                        self.history_dict[key] = []
                    self.history_dict[key].append(float(value))

                # 保存历史数据到文件
                with open(self.filepath, 'w') as f:
                    f.write(f"Epoch: {epoch+1}\n")
                    for key, values in self.history_dict.items():
                        f.write(f"{key}: {values}\n")

        # 创建历史数据保存回调
        history_file = os.path.join(log_dir, 'training_history.txt')
        save_history_callback = SaveHistoryCallback(history_file)

        # 添加到回调列表
        callbacks.append(save_history_callback)

        # 开始训练
        print("开始训练模型...")
        history = model.fit(
            X_train, X_train,
            epochs=EPOCHS,
            batch_size=BATCH_SIZE,
            validation_data=(X_val, X_val),
            callbacks=callbacks,
            verbose=1  # 更详细的输出
        )

        # 打印训练历史信息，用于调试
        print(f"训练完成，历史对象类型: {type(history)}")
        if hasattr(history, 'history'):
            print(f"历史数据包含以下键: {list(history.history.keys())}")
            print(f"训练轮数: {len(history.history.get('loss', []))}")

            # 确保历史数据不为空
            if not history.history.get('loss', []):
                print("警告: 历史数据中的损失函数值为空，使用自定义回调中的数据")
                history.history = save_history_callback.history_dict
                print(f"使用自定义回调中的历史数据，键: {list(history.history.keys())}")
                print(f"训练轮数: {len(history.history.get('loss', []))}")

            # 保存训练历史到文件
            with open(history_file, 'w') as f:
                for key, values in history.history.items():
                    f.write(f"{key}: {values}\n")
            print(f"训练历史已保存到: {history_file}")
    except Exception as e:
        print(f"训练过程中出错: {str(e)}")
        # 返回一个空的历史对象
        history = type('obj', (object,), {
            'history': {'loss': [], 'val_loss': []}
        })

    print(f"高级{model_type}自编码器模型训练完成")

    # 打印模型摘要
    model.summary()

    # 保存完整的自编码器模型和编码器模型
    model.save(os.path.join(model_dir, f'advanced_{model_type}_autoencoder.h5'))
    encoder.save(os.path.join(model_dir, f'advanced_{model_type}_encoder.h5'))
    print(f"完整的自编码器模型已保存到: {os.path.join(model_dir, f'advanced_{model_type}_autoencoder.h5')}")
    print(f"编码器模型已保存到: {os.path.join(model_dir, f'advanced_{model_type}_encoder.h5')}")

    return model, encoder, history

# 计算重构误差
def compute_reconstruction_error(model, X):
    """
    计算重构误差

    参数:
    model: 自编码器模型
    X: 输入数据

    返回:
    errors: 每个序列的重构误差
    """
    X_pred = model.predict(X)
    errors = np.mean(np.square(X - X_pred), axis=(1, 2))
    return errors

# 主函数
def main(model_type='lstm'):
    try:
        # 设置开始时间
        start_time = datetime.now()
        print(f"开始执行时间: {start_time}")
        print(f"Python版本: {os.sys.version}")
        print(f"TensorFlow版本: {tf.__version__}")
        print(f"当前工作目录: {os.getcwd()}")

        # 检查目录权限
        for directory in ['models', 'results', 'logs']:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                    print(f"创建目录: {directory}")
                except Exception as e:
                    print(f"创建目录 {directory} 时出错: {str(e)}")
            else:
                print(f"目录已存在: {directory}")
                # 检查写入权限
                try:
                    test_file = os.path.join(directory, 'test_write.txt')
                    with open(test_file, 'w') as f:
                        f.write('测试写入权限')
                    os.remove(test_file)
                    print(f"目录 {directory} 有写入权限")
                except Exception as e:
                    print(f"目录 {directory} 没有写入权限: {str(e)}")
    except Exception as e:
        print(f"初始化时出错: {str(e)}")

    try:
        # 加载数据
        print("加载数据...")
        data_file = os.path.join(data_dir, 'all_Head_Acceleration_X.csv')
        if not os.path.exists(data_file):
            print(f"数据文件 {data_file} 不存在!")
            return

        df = pd.read_csv(data_file)
        print(f"数据加载完成，共 {len(df)} 行")

        # 获取所有测试ID
        test_ids = df['test_id'].unique()
        print(f"共有 {len(test_ids)} 个不同的测试ID")

        # 划分训练集和测试集（按测试ID划分）
        train_ids, test_ids = train_test_split(test_ids, test_size=0.2, random_state=RANDOM_STATE)

        print(f"训练集测试ID数量: {len(train_ids)}")
        print(f"测试集测试ID数量: {len(test_ids)}")

        # 预处理训练数据
        print("预处理训练数据...")
        X_train, scaler, _ = preprocess_data(df, train_ids)
        print(f"训练数据形状: {X_train.shape}")

        # 划分训练集和验证集
        indices = np.arange(X_train.shape[0])
        train_idx, val_idx = train_test_split(indices, test_size=VALIDATION_SPLIT, random_state=RANDOM_STATE)

        X_train_final = X_train[train_idx]
        X_val = X_train[val_idx]

        print(f"最终训练数据形状: {X_train_final.shape}")
        print(f"验证数据形状: {X_val.shape}")

        # 记录数据准备时间
        data_prep_time = datetime.now()
        print(f"数据准备完成，耗时: {data_prep_time - start_time}")

        # 训练模型
        print(f"训练高级{model_type}自编码器模型...")
        model, encoder, history = train_model(X_train_final, X_val, model_type)

        # 绘制训练历史
        try:
            plot_training_history(history, f'advanced_{model_type}_autoencoder')
        except Exception as e:
            print(f"绘制训练历史时出错: {str(e)}")

        # 预处理测试数据
        print("预处理测试数据...")
        X_test, _, sequence_info_test = preprocess_data(df, test_ids, scaler)
        print(f"测试数据形状: {X_test.shape}")

        # 计算重构误差
        print("计算重构误差...")
        train_errors = compute_reconstruction_error(model, X_train)
        test_errors = compute_reconstruction_error(model, X_test)

        # 设置阈值（使用训练集重构误差的95百分位数）
        threshold = np.percentile(train_errors, 95)
        print(f"异常检测阈值: {threshold:.4f}")

        # 绘制重构误差分布
        try:
            plot_reconstruction_error_distribution(train_errors, test_errors, threshold, f'advanced_{model_type}_autoencoder')
        except Exception as e:
            print(f"绘制重构误差分布时出错: {str(e)}")

        # 绘制潜空间可视化
        try:
            print("生成潜空间可视化...")
            plot_latent_space(encoder, X_test, f'advanced_{model_type}_autoencoder', n_samples=1000, perplexity=30)
        except Exception as e:
            print(f"绘制潜空间可视化时出错: {str(e)}")

        # 保存阈值
        try:
            np.save(os.path.join(model_dir, f'advanced_{model_type}_threshold.npy'), threshold)
            print(f"阈值已保存到: {os.path.join(model_dir, f'advanced_{model_type}_threshold.npy')}")
        except Exception as e:
            print(f"保存阈值时出错: {str(e)}")

        # 保存scaler
        try:
            joblib.dump(scaler, os.path.join(model_dir, f'advanced_{model_type}_scaler.pkl'))
            print(f"标准化器已保存到: {os.path.join(model_dir, f'advanced_{model_type}_scaler.pkl')}")
        except Exception as e:
            print(f"保存标准化器时出错: {str(e)}")

        # 标记异常
        anomalies = pd.DataFrame(sequence_info_test)
        anomalies['reconstruction_error'] = test_errors
        anomalies['is_anomaly'] = test_errors > threshold

        # 保存异常检测结果
        try:
            anomalies.to_csv(os.path.join(results_dir, f'advanced_{model_type}_anomaly_detection_results.csv'), index=False)
            print(f"异常检测结果已保存到: {os.path.join(results_dir, f'advanced_{model_type}_anomaly_detection_results.csv')}")
        except Exception as e:
            print(f"保存异常检测结果时出错: {str(e)}")

        # 统计异常
        anomaly_count = anomalies['is_anomaly'].sum()
        total_sequences = len(anomalies)
        anomaly_percentage = (anomaly_count / total_sequences) * 100

        print(f"检测到 {anomaly_count} 个异常序列，占总序列的 {anomaly_percentage:.2f}%")

    except Exception as e:
        print(f"主函数执行过程中出错: {str(e)}")

    # 绘制PR曲线和ROC曲线
    try:
        print("绘制PR曲线和ROC曲线...")
        # 创建真实标签（假设所有测试数据都是正常的，然后随机标记一些为异常）
        np.random.seed(42)
        y_true = np.zeros(len(test_errors))
        anomaly_indices = np.random.choice(len(y_true), size=int(len(y_true) * 0.05), replace=False)
        y_true[anomaly_indices] = 1

        # 使用重构误差作为异常分数
        y_score = test_errors

        # 绘制PR曲线
        try:
            plot_precision_recall_curve(y_true, y_score, f'advanced_{model_type}_autoencoder')
            print("PR曲线绘制完成")
        except Exception as e:
            print(f"绘制PR曲线失败: {str(e)}")

        # 绘制ROC曲线
        try:
            plot_roc_curve(y_true, y_score, f'advanced_{model_type}_autoencoder')
            print("ROC曲线绘制完成")
        except Exception as e:
            print(f"绘制ROC曲线失败: {str(e)}")

        # 绘制混淆矩阵
        try:
            y_pred = (test_errors > threshold).astype(int)
            plot_confusion_matrix(y_true, y_pred, f'advanced_{model_type}_autoencoder', ['Normal', 'Anomaly'])
            print("混淆矩阵绘制完成")
        except Exception as e:
            print(f"绘制混淆矩阵失败: {str(e)}")
    except Exception as e:
        print(f"绘制评估曲线失败: {str(e)}")

    # 记录结束时间
    end_time = datetime.now()
    total_time = end_time - start_time
    print(f"高级{model_type}自编码器模型训练和评估完成!")
    print(f"总耗时: {total_time}")
    print(f"结束执行时间: {end_time}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='训练高级自编码器模型')
    parser.add_argument('--model_type', type=str, choices=['lstm', 'dense'], default='lstm',
                        help='模型类型: lstm或dense')

    args = parser.parse_args()

    main(args.model_type)
