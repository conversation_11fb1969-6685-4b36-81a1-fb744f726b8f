# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-05-15
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 数据集异常检测结果比较报告生成器
# 生成新旧客户数据集异常检测结果的详细比较报告，
# 包括异常百分比统计、可视化图表（条形图、箱线图）和统计分析。
# 用于评估模型在不同数据集上的表现差异。

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置目录
customer_data_dir = 'customer_data_results'
new_customer_data_dir = 'new_customer_data_results'
comparison_dir = 'comparison_results'
os.makedirs(comparison_dir, exist_ok=True)

def collect_anomaly_percentages(directory):
    """
    收集异常百分比数据

    参数:
    directory: 目录路径

    返回:
    results: 包含测试ID和异常百分比的字典
    """
    results = {}

    # 获取所有CSV文件
    csv_files = [f for f in os.listdir(directory) if f.endswith('_lstm_anomalies.csv')]

    for file in csv_files:
        # 提取测试ID
        test_id = file.split('_')[0]

        # 读取CSV文件
        file_path = os.path.join(directory, file)
        if os.path.exists(file_path):
            anomalies = pd.read_csv(file_path)
            anomaly_count = anomalies['is_anomaly'].sum()
            total_sequences = len(anomalies)
            anomaly_percentage = (anomaly_count / total_sequences) * 100

            results[test_id] = anomaly_percentage

    return results

def generate_comparison_chart():
    """
    生成比较图表
    """
    # 收集原有数据集的异常百分比
    original_results = collect_anomaly_percentages(customer_data_dir)

    # 收集新数据集的异常百分比
    new_results = collect_anomaly_percentages(new_customer_data_dir)

    # 创建DataFrame
    original_df = pd.DataFrame({
        'test_id': list(original_results.keys()),
        'anomaly_percentage': list(original_results.values()),
        'dataset': ['Original'] * len(original_results)
    })

    new_df = pd.DataFrame({
        'test_id': list(new_results.keys()),
        'anomaly_percentage': list(new_results.values()),
        'dataset': ['New'] * len(new_results)
    })

    # 合并DataFrame
    df = pd.concat([original_df, new_df], ignore_index=True)

    # 绘制条形图
    plt.figure(figsize=(15, 8))

    # 设置条形图的位置
    original_indices = np.arange(len(original_results))
    new_indices = np.arange(len(original_results), len(original_results) + len(new_results))

    # 绘制原有数据集的条形图
    plt.bar(original_indices, original_df['anomaly_percentage'], width=0.8, label='Original Dataset', color='blue', alpha=0.7)

    # 绘制新数据集的条形图
    plt.bar(new_indices, new_df['anomaly_percentage'], width=0.8, label='New Dataset', color='red', alpha=0.7)

    # 设置x轴标签
    plt.xticks(np.concatenate([original_indices, new_indices]),
              np.concatenate([original_df['test_id'], new_df['test_id']]),
              rotation=45)

    # 添加标题和标签
    plt.title('Anomaly Percentage Comparison between Original and New Datasets')
    plt.xlabel('Test ID')
    plt.ylabel('Anomaly Percentage (%)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加数值标签
    for i, v in enumerate(np.concatenate([original_df['anomaly_percentage'], new_df['anomaly_percentage']])):
        plt.text(i, v + 0.5, f"{v:.2f}%", ha='center')

    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(comparison_dir, 'anomaly_percentage_comparison.png'))
    plt.close()

    print(f"比较图表已保存到: {os.path.join(comparison_dir, 'anomaly_percentage_comparison.png')}")

    # 创建箱线图
    plt.figure(figsize=(10, 6))

    # 准备数据
    original_percentages = original_df['anomaly_percentage'].values
    new_percentages = new_df['anomaly_percentage'].values

    # 绘制箱线图
    plt.boxplot([original_percentages, new_percentages], labels=['Original Dataset', 'New Dataset'])

    # 添加标题和标签
    plt.title('Distribution of Anomaly Percentages')
    plt.ylabel('Anomaly Percentage (%)')
    plt.grid(True, alpha=0.3)

    # 保存图表
    plt.savefig(os.path.join(comparison_dir, 'anomaly_percentage_boxplot.png'))
    plt.close()

    print(f"箱线图已保存到: {os.path.join(comparison_dir, 'anomaly_percentage_boxplot.png')}")

    # 生成统计报告
    generate_statistical_report(original_df, new_df)

def generate_statistical_report(original_df, new_df):
    """
    生成统计报告

    参数:
    original_df: 原有数据集的DataFrame
    new_df: 新数据集的DataFrame
    """
    # 计算统计量
    original_mean = original_df['anomaly_percentage'].mean()
    original_std = original_df['anomaly_percentage'].std()
    original_min = original_df['anomaly_percentage'].min()
    original_max = original_df['anomaly_percentage'].max()

    new_mean = new_df['anomaly_percentage'].mean()
    new_std = new_df['anomaly_percentage'].std()
    new_min = new_df['anomaly_percentage'].min()
    new_max = new_df['anomaly_percentage'].max()

    # 创建报告
    report = []
    report.append("# 新旧数据集异常检测结果比较报告\n")

    report.append("## 统计摘要\n")
    report.append("| 统计量 | 原有数据集 | 新数据集 |\n")
    report.append("|--------|------------|----------|\n")
    report.append(f"| 平均异常百分比 | {original_mean:.2f}% | {new_mean:.2f}% |\n")
    report.append(f"| 标准差 | {original_std:.2f}% | {new_std:.2f}% |\n")
    report.append(f"| 最小值 | {original_min:.2f}% | {new_min:.2f}% |\n")
    report.append(f"| 最大值 | {original_max:.2f}% | {new_max:.2f}% |\n")

    report.append("\n## 原有数据集异常百分比\n")
    report.append("| 测试ID | 异常百分比 |\n")
    report.append("|--------|------------|\n")

    for _, row in original_df.sort_values('test_id').iterrows():
        report.append(f"| {row['test_id']} | {row['anomaly_percentage']:.2f}% |\n")

    report.append("\n## 新数据集异常百分比\n")
    report.append("| 测试ID | 异常百分比 |\n")
    report.append("|--------|------------|\n")

    for _, row in new_df.sort_values('test_id').iterrows():
        report.append(f"| {row['test_id']} | {row['anomaly_percentage']:.2f}% |\n")

    report.append("\n## 分析结论\n")

    # 比较平均异常百分比
    if new_mean > original_mean:
        report.append(f"1. 新数据集的平均异常百分比（{new_mean:.2f}%）高于原有数据集（{original_mean:.2f}%），表明新数据集可能包含更多异常。\n")
    elif new_mean < original_mean:
        report.append(f"1. 新数据集的平均异常百分比（{new_mean:.2f}%）低于原有数据集（{original_mean:.2f}%），表明新数据集可能包含较少异常。\n")
    else:
        report.append(f"1. 新数据集的平均异常百分比（{new_mean:.2f}%）与原有数据集（{original_mean:.2f}%）相同，表明两个数据集的异常水平相似。\n")

    # 比较标准差
    if new_std > original_std:
        report.append(f"2. 新数据集的异常百分比标准差（{new_std:.2f}%）高于原有数据集（{original_std:.2f}%），表明新数据集的异常分布更加分散。\n")
    elif new_std < original_std:
        report.append(f"2. 新数据集的异常百分比标准差（{new_std:.2f}%）低于原有数据集（{original_std:.2f}%），表明新数据集的异常分布更加集中。\n")
    else:
        report.append(f"2. 新数据集的异常百分比标准差（{new_std:.2f}%）与原有数据集（{original_std:.2f}%）相同，表明两个数据集的异常分布相似。\n")

    # 比较最大值
    if new_max > original_max:
        report.append(f"3. 新数据集的最大异常百分比（{new_max:.2f}%）高于原有数据集（{original_max:.2f}%），表明新数据集中存在更极端的异常样本。\n")
    elif new_max < original_max:
        report.append(f"3. 新数据集的最大异常百分比（{new_max:.2f}%）低于原有数据集（{original_max:.2f}%），表明新数据集中不存在极端异常样本。\n")
    else:
        report.append(f"3. 新数据集的最大异常百分比（{new_max:.2f}%）与原有数据集（{original_max:.2f}%）相同，表明两个数据集的极端异常水平相似。\n")

    # 保存报告
    report_file = os.path.join(comparison_dir, 'comparison_report.md')
    with open(report_file, 'w') as f:
        f.writelines(report)

    print(f"比较报告已保存到: {report_file}")

def main():
    # 生成比较图表
    generate_comparison_chart()

if __name__ == "__main__":
    main()
